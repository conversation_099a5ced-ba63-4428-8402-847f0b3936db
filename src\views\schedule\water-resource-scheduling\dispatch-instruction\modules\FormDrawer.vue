<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度令编码" prop="cmdCode">
              <a-input v-model="form.cmdCode" placeholder="请输入" allow-clear @input="onCmdCodeInput" />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度令名称" prop="cmdName">
              <a-input v-model="form.cmdName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度方案">
              <a-select
                show-search
                allow-clear
                v-model="form.dispatchId"
                :options="dispatchOptions"
                placeholder="请选择"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="计划工作时间" prop="planTime">
              <a-range-picker
                allow-clear
                showTime
                format="YYYY-MM-DD HH:mm"
                valueFormat="YYYY-MM-DD HH:mm"
                :placeholder="['开始时间', '结束时间']"
                v-model="form.planTime"
                :disabledDate="disabledDate"
                @change="onRangeChange"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div class="title">工程信息</div>
            <div class="project-table-container">
              <div class="table-header">
                <a-button type="primary" @click="addProjectRow" style="margin-bottom: 16px;">
                  <a-icon type="plus" />
                  新增工程
                </a-button>
              </div>
              <a-table
                :columns="projectColumns"
                :data-source="form.projectList"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: 800 }"
              >
                <template slot="projectName" slot-scope="text, record, index">
                  <a-select
                    v-model="record.projectId"
                    :options="projectOptions"
                    placeholder="请选择工程"
                    style="width: 100%"
                    @change="(value) => handleProjectSelectChange(value, index)"
                  />
                </template>
                <template slot="projectManager" slot-scope="text, record, index">
                  <a-select
                    v-model="record.wardUserId"
                    :options="record.userOptions || []"
                    placeholder="请选择负责人"
                    style="width: 100%"
                  />
                </template>
                <template slot="openTime" slot-scope="text, record, index">
                  <a-time-picker
                    v-model="record.openTime"
                    format="HH:mm"
                    placeholder="请选择开闸时间"
                    style="width: 100%"
                  />
                </template>
                <template slot="closeTime" slot-scope="text, record, index">
                  <a-time-picker
                    v-model="record.closeTime"
                    format="HH:mm"
                    placeholder="请选择关闸时间"
                    style="width: 100%"
                  />
                </template>
                <template slot="remark" slot-scope="text, record, index">
                  <a-input
                    v-model="record.remark"
                    placeholder="请输入备注"
                  />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a-button
                    type="link"
                    danger
                    @click="removeProjectRow(index)"
                  >
                    删除
                  </a-button>
                </template>
              </a-table>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: ['dispatchOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        projectOptions: [
          { label: '水库闸门工程', value: 1 },
          { label: '灌溉渠道工程', value: 2 },
          { label: '排水泵站工程', value: 3 },
        ],
        formTitle: '',
        projectColumns: [
          {
            title: '工程名称',
            dataIndex: 'projectName',
            key: 'projectName',
            width: 150,
            scopedSlots: { customRender: 'projectName' },
          },
          {
            title: '负责人',
            dataIndex: 'projectManager',
            key: 'projectManager',
            width: 120,
            scopedSlots: { customRender: 'projectManager' },
          },
          {
            title: '开闸时间',
            dataIndex: 'openTime',
            key: 'openTime',
            width: 120,
            scopedSlots: { customRender: 'openTime' },
          },
          {
            title: '关闸时间',
            dataIndex: 'closeTime',
            key: 'closeTime',
            width: 120,
            scopedSlots: { customRender: 'closeTime' },
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            width: 150,
            scopedSlots: { customRender: 'remark' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 80,
            scopedSlots: { customRender: 'action' },
          },
        ],
        form: {
          cmdCode: '',
          cmdName: '',
          dispatchId: null,
          planEndDate: null,
          planStartDate: null,
          planTime: [],
          projectList: [],
        },
        open: false,
        rules: {
          cmdCode: [
            { required: true, message: '调度令编码不能为空', trigger: 'blur' },
            { validator: this.validateCmdCode, trigger: 'blur' }
          ],
          cmdName: [{ required: true, message: '调度令名称不能为空', trigger: 'blur' }],
          planTime: [{ required: true, message: '计划工作时间不能为空', trigger: 'change' }],
        },
        debounceTimer: null,
      }
    },
    methods: {
      // 禁用今天之前的日期
      disabledDate(current) {
        return current && current < moment().startOf('day');
      },
      
      onRangeChange(dates) {
        this.form.planStartDate = dates ? dates[0] : null
        this.form.planEndDate = dates ? dates[1] : null
      },
      
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      
      // 新增工程行
      addProjectRow() {
        this.form.projectList.push({
          projectId: null,
          wardUserId: null,
          openTime: null,
          closeTime: null,
          remark: '',
          userOptions: [
            { label: '张三', value: 1 },
            { label: '李四', value: 2 },
            { label: '王五', value: 3 },
          ],
        })
      },
      
      // 删除工程行
      removeProjectRow(index) {
        this.form.projectList.splice(index, 1)
      },
      
      // 处理工程选择变化
      handleProjectSelectChange(value, index) {
        const project = this.projectOptions.find(item => item.value === value)
        if (project) {
          // 模拟加载用户选项
          const userOptions = [
            { label: '张三', value: 1 },
            { label: '李四', value: 2 },
            { label: '王五', value: 3 },
          ]
          this.$set(this.form.projectList[index], 'userOptions', userOptions)
          this.$set(this.form.projectList[index], 'wardUserId', null)
        }
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = row.action

        if (row.action == '修改' || row.action == '复制') {
          this.modalLoading = true

          // 模拟获取详情数据
          setTimeout(() => {
            this.form = {
              ...row,
              planTime: row.planWorkTime ? row.planWorkTime.split(' ~ ') : [],
              projectList: row.projectList ? [...row.projectList.map(item => ({
                ...item,
                projectId: 1, // 模拟映射
                wardUserId: 1, // 模拟映射
                openTime: item.openTime ? moment(item.openTime, 'YYYY-MM-DD HH:mm') : null,
                closeTime: item.closeTime ? moment(item.closeTime, 'YYYY-MM-DD HH:mm') : null,
                userOptions: [
                  { label: '张三', value: 1 },
                  { label: '李四', value: 2 },
                  { label: '王五', value: 3 },
                ],
              }))] : [],
            }

            if (row.action == '复制') {
              this.form.id = null
              this.form.cmdCode = ''
            }

            this.modalLoading = false
          }, 500)
        } else {
          // 新增时初始化
          this.form = {
            cmdCode: '',
            cmdName: '',
            dispatchId: null,
            planEndDate: null,
            planStartDate: null,
            planTime: [],
            projectList: [],
          }
        }
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            // 处理时间格式
            if (this.form.planTime && this.form.planTime.length === 2) {
              this.form.planStartDate = this.form.planTime[0]
              this.form.planEndDate = this.form.planTime[1]
            }

            // 处理工程信息时间格式
            this.form.projectList = this.form.projectList.map(item => ({
              ...item,
              openTime: item.openTime ? item.openTime.format('YYYY-MM-DD HH:mm') : '',
              closeTime: item.closeTime ? item.closeTime.format('YYYY-MM-DD HH:mm') : '',
            }))

            // 模拟提交
            setTimeout(() => {
              this.$message.success(this.form.id ? '修改成功' : '新增成功')
              this.loading = false
              this.open = false
              this.$emit('ok')
            }, 1000)
          }
        })
      },

      // 调度令编码输入防抖校验
      onCmdCodeInput() {
        if (this.debounceTimer) clearTimeout(this.debounceTimer)
        this.debounceTimer = setTimeout(() => {
          if (!this.form.cmdCode) return
          this.$refs.form.validateField('cmdCode')
        }, 2000)
      },

      // 调度令编码唯一性校验
      validateCmdCode(rule, value, callback) {
        if (!value) return callback()
        if (this.form.id) return callback() // 修改时不校验自己

        // 模拟校验唯一性
        const exists = ['DD202401001', 'DD202401002'].includes(value)
        if (exists) {
          callback(new Error('该调度令编码已存在'))
        } else {
          callback()
        }
      },
    }
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .project-table-container {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;

    .table-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }

  ::v-deep .ant-table-tbody > tr > td {
    padding: 8px;
  }

  ::v-deep .ant-table-thead > tr > th {
    padding: 8px;
    background-color: #f5f5f5;
  }
</style>
