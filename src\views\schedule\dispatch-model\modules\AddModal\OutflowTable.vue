<template>
  <div class="outflow-table-container">
    <div class="panel-header">
      <h3>出库过程</h3>
      <a-button type="primary" @click="handleBatchImport" class="batch-import-btn">
          批量导入
        </a-button>
    </div>
    
    <div class="table-section">
      <VxeTable
        ref="vxeTableRef"
        :columns="columns"
        :tableData="dataSource"
        size="small"
        :tablePage="false"
        :isShowTableHeader="false"
        :scroll="{ y: 'calc(100vh - 400px)' }"
      ></VxeTable>
    </div>
    <BatchImportModal
      :visible.sync="showBatchImport"
      :timeList="dataSource.map(item => item.time)"
      :dispatchMethod="dispatchMethod"
      @save="handleBatchImportSave"
    />
  </div>
</template>

<script>
import VxeTable from '@/components/VxeTable/index.vue'
import BatchImportModal from './BatchImportModal.vue'

export default {
  name: 'OutflowTable',
  components: {
    VxeTable,
    BatchImportModal,
  },
  props: {
    dataSource: Array,
    dispatchMethod: Number, // 调度方式：1-现状调度，2-推荐调度，3-手动调度
  },
  data() {
    return {
      hoveredRowIndex: -1, // 当前悬浮的行索引
      hoveredField: '', // 当前悬浮的字段
      showBatchImport: false,
    }
  },
  computed: {
    columns() {
      return [
        {
          title: '时间',
          field: 'time',
          minWidth: 120,
          fixed: 'left',
        },
        {
          title: '供水流量(m³/s)',
          field: 'supplyFlow',
          minWidth: 150,
          align: 'center',
          slots: {
            default: ({ row, rowIndex }) => {
              return this.renderFlowCell(row, rowIndex, 'supplyFlow')
            }
          }
        },
        {
          title: '泄洪流量(m³/s)',
          field: 'floodFlow',
          minWidth: 150,
          align: 'center',
          slots: {
            default: ({ row, rowIndex }) => {
              return this.renderFlowCell(row, rowIndex, 'floodFlow')
            }
          }
        },
        // {
        //   title: '下游需水流量(m³/s)',
        //   field: 'downFlow',
        //   minWidth: 150,
        //   align: 'center',
        //   slots: {
        //     default: ({ row }) => {
        //       return this.$createElement('span', {
        //         style: { color: '#666' }
        //       }, row.downFlow || 0)
        //     }
        //   }
        // },
      ]
    }
  },
  methods: {
    // 渲染流量单元格
    renderFlowCell(row, rowIndex, field) {
      const h = this.$createElement
      const isSupplyFlow = field === 'supplyFlow'
      
      // 判断是否可编辑
      let editable = false
      if (this.dispatchMethod === 1) {
        // 现状调度：不可编辑
        editable = false
      } else if (this.dispatchMethod === 2) {
        // 推荐调度：供水流量可编辑，泄洪流量不可编辑
        editable = isSupplyFlow
      } else if (this.dispatchMethod === 3) {
        // 手动调度：都可编辑
        editable = true
      }

      if (editable) {
        return h('div', {
          class: 'flow-cell',
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            width: '100%',
            padding: '4px'
          },
          on: {
            mouseenter: () => this.handleCellMouseEnter(rowIndex, field),
            mouseleave: () => this.handleCellMouseLeave()
          }
        }, [
          h('a-input-number', {
            props: {
              size: 'small',
              step: 0.01,
              min: 0,
              precision: 2,
              value: row[field]
            },
            style: {
              width: '100px'
            },
            on: {
              change: (value) => this.handleInputChange(rowIndex, field, value)
            }
          }),
          h('a', {
            style: {
              color: '#165DFF',
              textDecoration: 'none',
              fontSize: '12px',
              whiteSpace: 'nowrap',
              opacity: this.hoveredRowIndex === rowIndex && this.hoveredField === field ? 1 : 0,
              transition: 'opacity 0.2s ease',
              cursor: 'pointer'
            },
            on: {
              click: () => this.handleFillDown(rowIndex, field)
            }
          }, '向下填充')
        ])
      } else {
        // 不可编辑时显示纯文本
        return h('span', {
          style: {
            color: '#666',
            fontSize: '14px'
          }
        }, row[field])
      }
    },

    // 处理单元格鼠标进入
    handleCellMouseEnter(rowIndex, field) {
      this.hoveredRowIndex = rowIndex
      this.hoveredField = field
    },

    // 处理单元格鼠标离开
    handleCellMouseLeave() {
      this.hoveredRowIndex = -1
      this.hoveredField = ''
    },

    // 处理输入框数值变化
    handleInputChange(rowIndex, field, value) {
      const newData = [...this.dataSource]
      newData[rowIndex] = {
        ...newData[rowIndex],
        [field]: value || 0
      }
      this.$emit('dataChange', newData)
    },

    // 处理向下填充
    handleFillDown(fromIndex, field) {
      const fillValue = this.dataSource[fromIndex][field]
      const newData = [...this.dataSource]
      
      // 向下填充到所有后续行
      for (let i = fromIndex + 1; i < newData.length; i++) {
        newData[i] = {
          ...newData[i],
          [field]: fillValue
        }
      }
      
      this.$emit('dataChange', newData)
      
      // 隐藏悬浮状态
      this.hoveredRowIndex = -1
      this.hoveredField = ''
      
      this.$message.success(`已将${field === 'supplyFlow' ? '供水流量' : '泄洪流量'}向下填充`)
    },

    // 批量导入
    handleBatchImport() {
      if (this.dispatchMethod === 1) {
        this.$message.warning('现状调度模式下不支持批量导入')
        return
      }
      this.showBatchImport = true
    },

    // 批量导入保存
    handleBatchImportSave(importData) {
      // importData: [{time, supplyFlow, floodFlow?}]
      const newData = this.dataSource.map((item, idx) => {
        const importItem = importData[idx]
        if (!importItem) return { ...item }
        return {
          ...item,
          supplyFlow: importItem.supplyFlow,
          floodFlow: this.dispatchMethod === 3 ? importItem.floodFlow : item.floodFlow,
        }
      })
      this.$emit('dataChange', newData)
      this.showBatchImport = false
    },

    // 处理变化（保持向后兼容）
    handleChange(row, field, value) {
      const index = this.dataSource.findIndex(item => item.time === row.time)
      if (index !== -1) {
        const newData = [...this.dataSource]
        newData[index] = {
          ...newData[index],
          [field]: value
        }
        this.$emit('dataChange', newData)
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .outflow-table-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .batch-import-btn {
      border-color: #165DFF;
      color: #fff !important;
      
      &:hover {
        border-color: #3273FF;
        background-color: #3273FF;
      }
    }
  }

  .table-section {
    flex: 1;
    overflow: auto;
  }

  .flow-cell {
    transition: all 0.2s ease;
    border-radius: 4px;
    
    &:hover {
      background-color: rgba(22, 93, 255, 0.05);
    }
    
    a {
      transition: all 0.2s ease;
      
      &:hover {
        color: #3273FF !important;
        text-decoration: underline !important;
      }
    }
  }
</style> 