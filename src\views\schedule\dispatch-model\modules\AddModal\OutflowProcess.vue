<template>
  <div style="height: 100%; display: flex; padding: 16px; position: relative;">
    <!-- 加载遮罩层 -->
    <div v-if="forecastLoading" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.8); display: flex; justify-content: center; align-items: center; z-index: 1000;">
      <div style="text-align: center;">
        <a-spin size="large" />
        <div style="margin-top: 16px; font-size: 16px; color: #666;">模型调用中，请稍候...</div>
      </div>
    </div>

    <!-- 左侧控制面板和图表 -->
    <div style="width: 55%; padding-right: 12px">
      <!-- 出库参数来源方式（仅手动调度时显示） -->
      <div v-if="baseInfo.dispatchMethod === 3" style="margin-bottom: 16px">
        <h3 style="margin-bottom: 8px; font-weight: bold">出库参数来源方式：</h3>
        <div style="display: flex; align-items: center; gap: 12px;">
          <a-radio-group v-model="outflowSourceType" @change="changeOutflowSource" style="flex: 0.5">
            <a-radio :value="1">现状</a-radio>
            <a-radio :value="2">推荐</a-radio>
          </a-radio-group>
          
          <a-select
            v-model="selectedRecommendPlan"
            placeholder="请选择推荐方案"
            style="flex: 1"
            @change="changeRecommendPlan"
            :loading="recommendPlansLoading"
          >
            <a-select-option v-for="plan in recommendPlans" :key="plan.resvrDispId" :value="plan.resvrDispId">
              {{ plan.caseName }}
            </a-select-option>
          </a-select>
        </div>
      </div>

      <!-- 调度方式说明 -->
      <!-- <div v-if="baseInfo.dispatchMethod" style="margin-bottom: 16px; padding: 8px; background: #f6f8fa; border-radius: 4px; font-size: 12px; color: #666;">
        <div v-if="baseInfo.dispatchMethod === 1" style="color: #666;">
          <strong>现状调度：</strong>数据不可编辑，显示历史调度数据
        </div>
        <div v-else-if="baseInfo.dispatchMethod === 2" style="color: #1890ff;">
          <strong>推荐调度：</strong>供水流量可手动调整，泄洪流量由系统推荐
        </div>
        <div v-else-if="baseInfo.dispatchMethod === 3" style="color: #52c41a;">
          <strong>手动调度：</strong>供水、泄洪流量均可手动调整，支持图表交互编辑
        </div>
      </div> -->

      <!-- 缩放系数控制 -->
      <div v-if="baseInfo.dispatchMethod !== 1">
        <h3 style="margin-bottom: 8px;font-weight: bold; ">缩放系数：</h3>
        
        <div class="" style="display: flex;">
          <!-- 缩放对象选择（仅手动调度时显示） -->
          <div v-if="baseInfo.dispatchMethod === 3" style="margin-bottom: 8px">
            <a-select v-model="scaleTarget" style="flex:0.5">
              <a-select-option value="both">同步修改</a-select-option>
              <a-select-option value="supply">需水流量</a-select-option>
              <a-select-option value="flood">泄洪流量</a-select-option>
            </a-select>
          </div>
          
          <!-- 推荐调度提示 -->
          <!-- <div v-if="baseInfo.dispatchMethod === 2" style="margin-bottom: 8px; font-size: 12px; color: #666;">
            仅调整供水流量
          </div>
          -->
          <!-- 缩放系数控制器：滑动条和输入框在同一行 -->
          <div style="display: flex; align-items: center; gap: 12px; flex:1.5;">
            <a-slider
              v-model="scaleValue"
              :min="0.1"
              :max="5"
              :step="0.1"
              style="flex: 1;"
              @change="onScaleChange"
            />
            <a-input-number
              v-model="scaleValue"
              :min="0.1"
              :max="5"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 80px"
              @change="onScaleChange"
            />
          </div>
        </div>
      </div>

      <!-- 折线图 -->
      <div style="height: 450px; border-radius: 4px;">
        <OutflowChart 
          :chartData="chartData" 
          :dispatchMethod="baseInfo.dispatchMethod"
          @dataChange="onChartDataChange"
          @toggleEdit="toggleEditMode"
        />
      </div>
    </div>

    <!-- 右侧数据表格 -->
    <div style="width: 45%; padding-left: 12px">
      <OutflowTable 
        :dataSource="tableData"
        :dispatchMethod="baseInfo.dispatchMethod"
        @dataChange="onTableDataChange"
      />
    </div>

  </div>
</template>

<script>
import OutflowChart from './OutflowChart.vue'
import OutflowTable from './OutflowTable.vue'
import moment from 'moment'
import { debounce } from 'lodash'
import { listOutFlow, getResvrDispPage, forecast } from '../../services'

export default {
  name: 'OutflowProcess',
  components: {
    OutflowChart,
    OutflowTable
  },
  props: ['baseInfo'],
  data() {
    return {
      outflowSourceType: 1, // 1-现状, 2-推荐
      selectedRecommendPlan: undefined,
      recommendPlans: [],
      recommendPlansLoading: false,
      scaleTarget: 'both', // both-同步修改, supply-供水流量, flood-泄洪流量
      scaleValue: 1,
      originalData: [],
      tableData: [],
      editMode: false,
      importData: '',
      forecastLoading: false // 预报接口调用加载状态
    }
  },
  computed: {
    chartData() {
      return [
        {
          name: '需水流量',
          data: this.tableData.map(item => [item.time, item.supplyFlow]),
          color: '#1890ff'
        },
        {
          name: '泄洪流量',
          data: this.tableData.map(item => [item.time, item.floodFlow]),
          color: '#52c41a'
        },
        // {
        //   name: '下游需水流量',
        //   data: this.tableData.map(item => [item.time, item.downFlow || 0]),
        //   color: '#ff7875'
        // }
      ]
    }
  },
  created() {
    // 手动调度时，先加载推荐方案，然后再加载出库数据
    if (this.baseInfo.dispatchMethod === 3) {
      this.loadRecommendPlans()
    } else {
      // 其他调度方式直接加载出库数据
      this.loadOutflowData()
    }
  },
  methods: {
    // 加载推荐方案数据
    loadRecommendPlans() {
      this.recommendPlansLoading = true
      const params = {
        dispathType: this.outflowSourceType === 1 ? 1 : 2, // 1-现状调度, 2-推荐调度
        pageNum: 1,
        pageSize: 10,
        scene: this.baseInfo.scene // 应用场景
      }

      getResvrDispPage(params).then(res => {
        this.recommendPlansLoading = false
        if (res.success && res.data && res.data.data) {
          this.recommendPlans = res.data.data
          // 默认选择第一项
          if (this.recommendPlans.length > 0) {
            this.selectedRecommendPlan = this.recommendPlans[0].resvrDispId
            // 选择第一项后加载出库数据
            this.loadOutflowDataWithPlan()
          } else {
            // 没有推荐方案时，使用默认参数加载数据
            this.loadOutflowData()
          }
        } else {
          console.error('获取推荐方案失败:', res.message)
          this.$message.error('获取推荐方案失败')
          // 失败时使用默认参数加载数据
          this.loadOutflowData()
        }
      }).catch(err => {
        this.recommendPlansLoading = false
        console.error('调用推荐方案接口失败:', err)
        this.$message.error('调用推荐方案接口失败')
        // 失败时使用默认参数加载数据
        this.loadOutflowData()
      })
    },

    // 使用选中的推荐方案加载出库数据
    loadOutflowDataWithPlan() {
      const params = {
        dispathType: 3, // 手动调度
        endTime: this.baseInfo.endTime,
        inWaterId: this.baseInfo.inWaterId,
        resvrDispId: this.selectedRecommendPlan,
        scene: this.baseInfo.scene,
        startTime: this.baseInfo.startTime
      }

      this.callListOutFlowAPI(params)
    },

    // 加载出库过程数据
    loadOutflowData() {
      const params = {
        dispathType: this.baseInfo.dispathType || this.baseInfo.dispatchMethod, // 调度方式
        endTime: this.baseInfo.endTime, // 预报结束时间
        inWaterId: this.baseInfo.inWaterId, // 来水预报id
        resvrDispId: 0, // 水库调度id(不用传值)
        scene: this.baseInfo.scene, // 应用场景
        startTime: this.baseInfo.startTime // 预报开始时间
      }

      this.callListOutFlowAPI(params)
    },

    // 调用listOutFlow接口的通用方法
    callListOutFlowAPI(params) {
      listOutFlow(params).then(res => {
        if (res.success && res.data) {
          this.processOutflowData(res.data)
        } else {
          console.error('获取出库过程数据失败:', res.message)
          this.$message.error('获取出库过程数据失败')
        }
      }).catch(err => {
        console.error('调用出库过程接口失败:', err)
        this.$message.error('调用出库过程接口失败')
      })
    },

    // 处理接口返回的出库过程数据
    processOutflowData(data) {
      this.tableData = []

      data.forEach(item => {
        // 处理时间格式
        const time = moment(item.tm).format('YYYY-MM-DD HH:mm')

        // 添加出库数据
        this.tableData.push({
          time,
          supplyFlow: item.supplyFlow || 0, // 供水流量
          floodFlow: item.floodFlow || 0, // 泄洪流量
          downFlow: item.downFlow || 0 // 下游需水流量，保存到数据中
        })
      })

      // 如果接口没有返回数据，显示提示信息
      if (this.tableData.length === 0) {
        this.$message.warning('接口未返回出库过程数据')
        return
      }

      // 保存原始数据，包含downFlow
      this.originalData = JSON.parse(JSON.stringify(this.tableData))
    },

    changeOutflowSource() {
      // 手动调度时，根据选择的现状/推荐重新加载对应的推荐方案
      if (this.baseInfo.dispatchMethod === 3) {
        // 重置选中的推荐方案
        this.selectedRecommendPlan = undefined
        // 重新加载推荐方案（dispathType会根据outflowSourceType自动设置）
        this.loadRecommendPlans()
      } else {
        // 其他调度方式的处理逻辑
        this.loadOutflowData()
      }
    },

    changeRecommendPlan() {
      // 根据选择的推荐方案重新加载数据
      if (this.baseInfo.dispatchMethod === 3) {
        this.loadOutflowDataWithPlan()
      } else {
        // 非手动调度时重新加载出库数据
        this.loadOutflowData()
      }
    },

    onScaleChange() {
      if (this.baseInfo.dispatchMethod === 1) return // 现状调度不允许修改

      this.tableData = this.originalData.map(item => {
        const newItem = { ...item }

        // 推荐调度时只能调整供水流量
        if (this.baseInfo.dispatchMethod === 2) {
          newItem.supplyFlow = Math.round(item.supplyFlow * this.scaleValue)
          // 泄洪流量保持不变
          newItem.floodFlow = item.floodFlow
        } else {
          // 手动调度时根据选择的目标进行缩放
          if (this.scaleTarget === 'both' || this.scaleTarget === 'supply') {
            newItem.supplyFlow = Math.round(item.supplyFlow * this.scaleValue)
          }
          if (this.scaleTarget === 'both' || this.scaleTarget === 'flood') {
            newItem.floodFlow = Math.round(item.floodFlow * this.scaleValue)
          }
        }

        return newItem
      })
    },

    onTableDataChange(newData) {
      this.tableData = newData
      // 更新原始数据以便缩放功能正常工作
      this.originalData = JSON.parse(JSON.stringify(newData))
      this.scaleValue = 1
    },

    onChartDataChange(changeData) {
      // 图表数据变化时更新表格数据
      const { seriesIndex, dataIndex, newValue } = changeData

      // 创建新的表格数据
      const newTableData = [...this.tableData]

      // 根据系列索引确定要更新的字段
      if (seriesIndex === 0) {
        // 供水流量 - 保持两位小数精度
        newTableData[dataIndex] = {
          ...newTableData[dataIndex],
          supplyFlow: Math.round(newValue * 100) / 100
        }
      } else if (seriesIndex === 1) {
        // 泄洪流量 - 保持两位小数精度
        newTableData[dataIndex] = {
          ...newTableData[dataIndex],
          floodFlow: Math.round(newValue * 100) / 100
        }
      }

      this.tableData = newTableData

      // 同时更新原始数据以便缩放功能正常工作
      this.originalData = JSON.parse(JSON.stringify(this.tableData))
      this.scaleValue = 1
    },

    toggleEditMode() {
      this.editMode = !this.editMode
    },

    save() {
      // 使用防抖函数，防止快速多次点击
      this.debouncedSave()
    },

    // 防抖保存方法
    debouncedSave: debounce(function() {
      // 手动调度且选择推荐方案时，检查是否已选择推荐方案
      if (this.baseInfo.dispatchMethod === 3) {
        if (!this.selectedRecommendPlan || this.recommendPlans.length === 0) {
          this.$message.warning('请选择推荐方案')
          return
        }
      }

      // 调用预报接口
      this.callForecastAPI()
    }, 3000), // 3秒防抖延迟

    // 调用预报接口
    callForecastAPI() {
      // 显示页面加载状态
      this.forecastLoading = true

      // 构建出库过程数据，从listOutFlow接口返回的数据中获取downFlow
      const outFlowList = this.tableData.map((item, index) => {
        return {
          tm: item.time,
          supplyFlow: item.supplyFlow,
          floodFlow: item.floodFlow,
          downFlow: item.downFlow || 0 // 下游需水流量直接从tableData获取
        }
      })

      const params = {
        caseName: this.baseInfo.caseName || '',
        dispathType: this.baseInfo.dispatchMethod || 0,
        endTime: this.baseInfo.endTime || '',
        inWaterId: this.baseInfo.inWaterId || 0,
        outFlowList: outFlowList,
        scene: this.baseInfo.scene || 0,
        startTime: this.baseInfo.startTime || ''
      }

      forecast(params).then(res => {
        this.forecastLoading = false
        if (res.success && res.data) {
          // 预报成功，传递resvrDispId到下一步
          this.$emit('saveData', {
            outflowSourceType: this.outflowSourceType,
            selectedRecommendPlan: this.selectedRecommendPlan,
            scaleValue: this.scaleValue,
            scaleTarget: this.scaleTarget,
            tableData: this.tableData,
            resvrDispId: res.data // 模型返回的resvrDispId
          })
        } else {
          this.$message.error('预报调用失败: ' + (res.message || '未知错误'))
        }
      }).catch(err => {
        this.forecastLoading = false
        console.error('调用预报接口失败:', err)
        this.$message.error('调用预报接口失败')
      })
    },
  }
}
</script>

<style lang="less" scoped>
</style> 