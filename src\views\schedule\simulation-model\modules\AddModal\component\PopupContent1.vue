<template>
  <div class="container">
    <div class="header">
      <div class="name">{{ '桃花江水库' }}</div>
      <a-icon type="close" style="cursor: pointer" @click="item.onPopupClose(item)" />
    </div>

    <div class="indicator">
      <div class="label"><span style="color:#1d2129;">{{ "来水方案: " }} </span>
        <a-select style="height:30px; width: 100px;" allow-clear size="small" v-model="newItem.dispatchCase"
          :options="item.dispatchCaseOptions" @change="handleDispatchCaseChange"></a-select>
        <a-button type="primary" size="small" style="height: 20px; margin-left: 3px;"
          @click.stop="item.onProcessClick(newItem)">确定</a-button>
      </div>
      <div class="label"><span style="color:#1d2129;">{{ "最大可供水量: " }} </span>
        <a-input placeholder="请输入流量值" style="width: 80px; height: 20px;" size="small" v-model="newItem.zdWater"
          disabled />
        <span style="margin-left: 4px; color: #1d2129; font-size: 12px;">万m³</span>
      </div>
      <div class="label" style="margin-top: 2px;"><span style="color:#1d2129;">{{ "实际可供水量: " }}</span>
        <a-input placeholder="请输入水位值" style="width: 80px;height: 20px;" size="small" v-model="newItem.sjWater" />
        <span style="margin-left: 4px; color: #1d2129; font-size: 12px;">万m³</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getMaxOutWater } from '../../../services'

export default {
  name: 'PopupContent',
  props: ['item'],
  data() {
    return {
      newItem: {
        dispatchCase: '',
        zdWater: 0,
        sjWater: 0,
      }
    }
  },
  created() {
    this.newItem.dispatchCase = this.item.dispatchCaseOptions[0].value;
    // 初始化时设置水量数据
    this.updateWaterData();
  },
  methods: {
    // 更新水量数据
    updateWaterData() {
      if (this.item.maxOutWaterData) {
        this.newItem.zdWater = this.item.maxOutWaterData.maxOutWater || 0; // 最大可供水量(万m³)
        // 实际可供水量默认取最大可供水量值
        this.newItem.sjWater = this.item.maxOutWaterData.maxOutWater || 0;
      }
    },

    // 处理来水方案切换
    async handleDispatchCaseChange(value) {
      if (!value) return;

      try {
        const response = await getMaxOutWater({ inWaterId: value });
        console.log('弹窗中获取来水方案数据响应:', response);

        if (response && response.success && response.data) {
          this.newItem.zdWater = response.data.maxOutWater || 0; // 最大可供水量(万m³)
          // 实际可供水量默认取最大可供水量值
          this.newItem.sjWater = response.data.maxOutWater || 0;
        }
      } catch (error) {
        console.error('弹窗中获取来水方案数据失败:', error);
        this.$message.error('获取来水方案数据失败');
      }
    }
  },
  watch: {
    // 监听父组件传入的maxOutWaterData变化
    'item.maxOutWaterData': {
      handler() {
        this.updateWaterData();
      },
      deep: true
    }
  }
}
</script>
<style lang="less">
.mapboxgl-popup-content {
  padding: 0;
}
</style>
<style lang="less" scoped>
.container {
  width: 235px;
  max-height: 130px;
  position: relative;
  display: flex;
  flex-direction: column;

  .header {
    background: #12E952;
    font-weight: 500;
    color: #FFF;
    line-height: 20px;
    font-size: 12px;
    padding: 4px 4px;
    display: flex;
    align-items: center;

    .icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #0d9c47;
      color: #fff;
      display: inline-block;
      text-align: center;
      line-height: 20px;
    }

    .name {
      flex: 1;
      margin: 0 13px 0 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .indicator {
    display: flex;
    flex-direction: column;
    padding: 15px 15px;

    .label {
      font-size: 12px;
      color: #4E5969;
    }

    .value {
      color: #1d2129;
    }
  }
}
</style>
