<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="事件编号">
        <a-input
          v-model="queryParam.eventCode"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="响应等级">
        <a-select
          show-search
          placeholder="请选择"
          v-model="queryParam.responseLevel"
          option-filter-prop="children"
        >
          <a-select-option value="I">I级</a-select-option>
          <a-select-option value="II">II级</a-select-option>
          <a-select-option value="III">III级</a-select-option>
          <a-select-option value="IV">IV级</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="启动时间">
        <a-range-picker
          allow-clear
          :value="startTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
import FormDetails from "./modules/FormDetails.vue";
import VxeTable from "@/components/VxeTable";
import VxeTableForm from "@/components/VxeTableForm";
import moment from "moment";

export default {
  name: "EmergencyResponse",
  components: {
    VxeTable,
    VxeTableForm,
    FormDetails,
  },
  data() {
    return {
      isChecked: false,
      showFormDetails: false,
      startTimes: [],

      list: [
        {
          id: 1,
          eventCode: "*********",
          responseLevel: "I",
          responseCondition: "水位超过警戒线10cm，降雨量达到50mm/h",
          startTime: "2024-01-15 08:30:00",
          chargeName: "张三",
        },
        {
          id: 2,
          eventCode: "ER2024002",
          responseLevel: "II",
          responseCondition: "水位超过警戒线5cm",
          startTime: "2024-01-20 14:20:00",
          chargeName: "李四",
        },
      ],
      tableTitle: "应急响应",
      loading: false,
      total: 2,
      selectIds: [],
      queryParam: {
        eventCode: "",
        responseLevel: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10,
        sort: [],
        startTime: null,
      },
      columns: [
        {
          type: "seq",
          title: "序号",
          width: 50,
          slots: {
            default: ({ row, rowIndex }) => {
              return (
                rowIndex +
                1 +
                (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              );
            },
          },
        },
        {
          title: "事件编号",
          field: "eventCode",
        },
        {
          title: "响应等级",
          field: "responseLevel",
          slots: {
            default: ({ row, rowIndex }) => {
              return row.responseLevel + "级";
            },
          },
        },
        {
          title: "响应条件",
          field: "responseCondition",
        },
        {
          title: "启动时间",
          field: "startTime",
        },
        {
          title: "负责人",
          field: "chargeName",
        },
        {
          title: "操作",
          field: "operate",
          width: 80,
          align: "left",
          slots: {
            default: ({ row, rowIndex }) => {
              return (
                <span>
                  <a onClick={() => this.handleDetails(row)}>查看</a>
                </span>
              );
            },
          },
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    adaptPageSizeChange(pageSize) {
      this.queryParam.pageSize = pageSize;
      this.getList();
    },
    onRangeChange(value, startTimes) {
      this.startTimes = value;
      if (startTimes.length == 0) {
        return;
      }
      this.queryParam.startTime = startTimes[0]
        ? moment(startTimes[0]).format("YYYY-MM-DD")
        : undefined;
      this.queryParam.endTime = startTimes[1]
        ? moment(startTimes[1]).format("YYYY-MM-DD")
        : undefined;
    },
    /** 查询列表 */
    getList() {
      this.showFormDetails = false;
      this.loading = true;
      this.selectChange({ records: [] });

      // 模拟API调用
      setTimeout(() => {
        let filteredList = [...this.list];

        // 根据查询条件过滤数据
        if (this.queryParam.eventCode) {
          filteredList = filteredList.filter(item =>
            item.eventCode.includes(this.queryParam.eventCode)
          );
        }

        if (this.queryParam.responseLevel) {
          filteredList = filteredList.filter(item =>
            item.responseLevel === this.queryParam.responseLevel
          );
        }

        this.total = filteredList.length;
        this.loading = false;
      }, 500);
    },
    // 多选框选中
    selectChange(valObj) {
      this.selectIds = valObj.records.map((item) => item.id);
      this.isChecked = !!valObj.records.length;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParam.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParam = {
        ...this.queryParam,
        eventCode: "",
        responseLevel: null,
        endTime: null,
        pageNum: 1,
        sort: [],
        startTime: null,
      };
      this.startTimes = [];
      this.handleQuery();
    },

    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.queryParam.pageNum = currentPage;
      this.queryParam.pageSize = pageSize;
      this.getList();
    },
    // 导出
    handleExport() {
      this.$message.info("导出功能待实现");
    },
    /* 查看 */
    handleDetails(record) {
      this.showFormDetails = true;
      this.$nextTick(() => this.$refs.formDetailsRef.details(record));
    },

    // 操作完成后
    onOperationComplete() {
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped></style>