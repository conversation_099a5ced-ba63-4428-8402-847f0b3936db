<template>
  <div style="flex: 1; width: 100%; height: 100%; display: flex; flex-direction: column;">
    <!-- 加载进度显示 -->
    <div v-if="showProgress" class="progress-container"
      style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 40px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <div class="step-name">{{ processInfo.stepname || '正在处理...' }}</div>
        <div class="progress-wrapper">
          <a-progress :percent="processInfo.process || 0" :show-info="true" stroke-color="#1890ff"
            :format="percent => `${percent}%`" />
        </div>
        <div v-if="processInfo.remaintime > 0" class="remain-time">
          预计剩余时间：{{ processInfo.remaintime }} 秒
        </div>
      </div>
    </div>

    <!-- 完成状态显示 -->
    <div v-else-if="showCompleted" class="completed-container"
      style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 40px;">
      <div style="text-align: center;">
        <a-icon type="check-circle" class="success-icon" />
        <div class="success-text">方案已生成</div>
        <div class="view-result-btn" @click="handleViewResult">
          <span style="color: #86909C; font-size: 14px;">点击查看</span><span
            style="color: #165DFF; font-size: 14px; font-weight: bold;">调度结果</span>
        </div>
      </div>
    </div>

    <!-- 错误状态显示 -->
    <div v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <!-- 调度结果面板 -->
    <div v-else-if="!!chSimId && showResultPanel"
      style="flex: 1; height: 100%; width: 100%; display: flex; overflow: hidden;">
      <!-- 左侧闸门树 -->
      <div class="left-panel"
        style="width: 300px; border-right: 1px solid #e8e8e8; padding: 16px; display: flex; flex-direction: column; overflow: hidden;">
        <div class="panel-title">闸门选择</div>
        <div class="tree-container" style="flex: 1; overflow: hidden;">
          <MultiTree :treeOptions="treeOptions" :currentKeys="selectedKeys" @check="handleTreeCheck"
            @onTreeMounted="handleTreeMounted" />
        </div>
      </div>

      <!-- 右侧统计图 -->
      <div class="right-panel" style="flex: 1; padding: 16px;">
        <div class="panel-title">流量统计图</div>
        <div class="chart-container" style="height: 400px; width: 100%;">
          <LineEchart :height="'400px'" :width="'100%'" :dataSource="chartDataSource" :custom="chartCustom"
            ref="flowChartRef" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
import { getModelRunProcess, getInferRes } from '../../services'
import MultiTree from '@/components/TreeGeneral/multiTree.vue'
import { LineEchart } from '@/components/Echarts'

export default {
  name: 'ScheduleResult',
  props: ['baseInfo', 'projectFlows', 'chSimId'],
  components: {
    MultiTree,
    LineEchart,
  },
  data() {
    return {
      loading: false,
      errorInfo: null,
      processInfo: {
        isover: 0,
        mid: '',
        moditime: '',
        msg: '',
        process: 0,
        remaintime: 0,
        stepname: ''
      },
      pollTimer: null,
      showResultPanel: false,

      // 树相关数据
      treeOptions: {
        dataSource: [],
        replaceFields: {
          key: 'key',
          title: 'title',
          children: 'children'
        }
      },
      selectedKeys: [],
      treeData: [], // 原始树数据

      // 图表相关数据
      chartDataSource: [],
      chartCustom: {
        shortValue: false,
        dataZoom: true,
        showAreaStyle: false,
        xLabel: '时间',
        yLabel: '流量(m³/s)',
        legend: true,
        grid: {
          left: '10%',
          right: '10%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
      },

      // 调度结果数据
      scheduleData: null,
    }
  },
  computed: {
    showProgress() {
      return this.chSimId && this.processInfo.isover === 0
    },
    showCompleted() {
      return this.chSimId && this.processInfo.isover === 1 && this.processInfo.process === 100 && !this.showResultPanel
    }
  },
  watch: {
    chSimId: {
      handler(newVal) {
        if (newVal) {
          this.startPolling()
        }
      },
      immediate: true
    }
  },
  created() {
    this.$emit('update:isDisabledBtn', true)
    this.errorInfo = null
  },
  mounted() { },
  beforeDestroy() {
    this.stopPolling()
  },
  methods: {
    // 开始轮询获取进度
    startPolling() {
      if (!this.chSimId) return

      this.getProgress()
      this.pollTimer = setInterval(() => {
        this.getProgress()
      }, 6000) // 每6秒轮询一次
    },

    // 停止轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
    },

    // 获取模型运行进度
    async getProgress() {
      try {
        const response = await getModelRunProcess({ chSimId: this.chSimId })
        console.log('模型进度响应:', response)

        if (response.data) {
          this.processInfo = response.data

          // 如果完成了，停止轮询并启用按钮
          if (response.data.isover === 1 && response.data.process === 100) {
            this.stopPolling()
            this.$emit('update:isDisabledBtn', false)
          }

          // 如果有错误信息，显示错误
          if (response.data.msg) {
            this.errorInfo = response.data.msg
            this.stopPolling()
          }
        }
      } catch (error) {
        console.error('获取模型进度失败:', error)
        this.errorInfo = '获取模型进度失败，请重试'
        this.stopPolling()
      }
    },

    // 查看调度结果
    async handleViewResult() {
      console.log('查看调度结果，chSimId:', this.chSimId)

      try {
        // 获取调度结果数据
        const response = await getInferRes({ chSimId: this.chSimId })
        console.log('调度结果响应:', response)

        if (response.data && response.data.length > 0) {
          this.scheduleData = response.data
          this.buildTreeData()
          this.showResultPanel = true
        } else {
          this.errorInfo = '获取调度结果失败，无数据'
        }
      } catch (error) {
        console.error('获取调度结果失败:', error)
        this.errorInfo = '获取调度结果失败，请重试'
      }
    },

    // 构建树数据
    buildTreeData() {
      if (!this.scheduleData || this.scheduleData.length === 0) return

      const treeData = []

      // 按渠道分组构建树结构
      this.scheduleData.forEach(channel => {
        const channelNode = {
          key: channel.projectCode,
          title: channel.projectName,
          type: 'category',
          children: []
        }

        // 只取resVOS的第一项的records数组来构成树结构
        if (channel.resVOS && channel.resVOS.length > 0) {
          const firstResVO = channel.resVOS[0]
          if (firstResVO.records && firstResVO.records.length > 0) {
            firstResVO.records.forEach(record => {
              channelNode.children.push({
                key: `${channel.projectCode}_${record.projectCode}`,
                title: record.projectName,
                type: 'data',
                projectCode: record.projectCode,
                projectName: record.projectName,
                channelCode: channel.projectCode,
                channelName: channel.projectName,
                data: record
              })
            })
          }
        }

        if (channelNode.children.length > 0) {
          treeData.push(channelNode)
        }
      })

      this.treeData = treeData
      this.treeOptions.dataSource = treeData
    },

    // 树节点选择事件
    handleTreeCheck(keys, nodes) {
      this.selectedKeys = keys
      this.updateChart(nodes)
    },

    // 树挂载完成事件
    handleTreeMounted(keys, nodes) {
      this.selectedKeys = keys
      this.updateChart(nodes)
    },

    // 更新图表数据
    updateChart(selectedNodes) {
      if (!selectedNodes || selectedNodes.length === 0) {
        this.chartDataSource = []
        return
      }

      const chartData = []
      const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa541c']

      selectedNodes.forEach((node, index) => {
        if (node.type === 'data' && node.data) {
          // 构建时间序列数据
          const timeSeriesData = []

          // 从调度结果数据中获取该工程的所有时间点数据
          const channelData = this.scheduleData.find(channel => channel.projectCode === node.channelCode)
          if (channelData && channelData.resVOS) {
            channelData.resVOS.forEach(resVO => {
              const record = resVO.records.find(r => r.projectCode === node.projectCode)
              if (record && resVO.tm) {
                timeSeriesData.push([resVO.tm, record.flow])
              }
            })
          }

          // 按时间排序
          timeSeriesData.sort((a, b) => new Date(a[0]) - new Date(b[0]))

          chartData.push({
            name: node.projectName,
            color: colors[index % colors.length],
            data: timeSeriesData
          })
        }
      })

      this.chartDataSource = chartData
    },

    save() {
      this.$emit('saveData', true)
    },
  },
}
</script>

<style lang="less" scoped>
.progress-container {
  .step-name {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .progress-wrapper {
    width: 400px;
    margin: 0 auto;

    ::v-deep .ant-progress-text {
      color: #1890ff;
      font-weight: 500;
    }
  }

  .remain-time {
    margin-top: 10px;
    color: #666;
    font-size: 14px;
  }
}

.completed-container {
  .success-icon {
    font-size: 48px;
    color: #52c41a;
    margin-bottom: 20px;
  }

  .success-text {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .view-result-btn {
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}
.tree-container {
  overflow-y: auto !important;
}
.left-panel {
  background: #fafafa;
}

.chart-container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  background: #fff;
}
</style>