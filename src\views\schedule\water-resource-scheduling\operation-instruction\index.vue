<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="操作令编号">
        <a-input v-model="queryParam.operateCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="操作日期">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.optTime"
          @change="onRangeChange"
        />
      </a-form-item>

      <a-form-item label="调度令编号">
        <a-input v-model="queryParam.cmdCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
        </VxeTable>
        <OptCmdDetail v-if="showForm" ref="formRef" @close="showForm = false" />
        <DispatchDetail v-if="dispatchShowForm" ref="dispatchFormRef" @close="dispatchShowForm = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import OptCmdDetail from './modules/OptCmdDetail.vue'
  import DispatchDetail from '../dispatch-instruction/modules/DetailModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'OperationInstruction',
    components: { VxeTable, VxeTableForm, OptCmdDetail, DispatchDetail },
    data() {
      return {
        isChecked: false,
        showForm: false,
        dispatchShowForm: false,
        list: [],
        tableTitle: '操作指令',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          operateCode: '',
          cmdCode: '',
          startDate: null,
          endDate: null,
          optTime: [],
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        // 测试数据
        mockData: [
          {
            id: 1,
            operateCode: 'CZ202401001',
            projectName: '水库闸门工程',
            operateContent: '开启1号闸门至50%开度',
            cmdCode: 'DD202401001',
            cmdName: '春季防汛调度指令',
            operateName: '张三',
            guardianName: '李四',
            operateTime: '2024-03-15 08:30 - 2024-03-15 09:00',
            startDate: '2024-03-15 08:30:00',
            endDate: '2024-03-15 09:00:00',
            operateDate: '2024-03-15',
            remark: '操作顺利完成，闸门开度达到预期',
            // 关联的调度指令详情
            dispatchDetail: {
              id: 1,
              cmdCode: 'DD202401001',
              cmdName: '春季防汛调度指令',
              dispatchCode: '防汛调度方案A',
              planWorkTime: '2024-03-15 08:00 ~ 2024-03-15 18:00',
              workManager: '张三',
              status: 3,
              projectList: [
                {
                  projectName: '水库闸门工程',
                  manager: '李四',
                  openTime: '2024-03-15 08:00',
                  closeTime: '2024-03-15 18:00',
                  remark: '注意安全操作'
                }
              ]
            },
            // 操作项目详情
            operateDetails: [
              { content: '检查闸门状态', status: 1 },
              { content: '启动闸门控制系统', status: 1 },
              { content: '开启闸门至50%开度', status: 1 },
              { content: '确认水位变化', status: 1 },
              { content: '记录操作数据', status: 1 }
            ]
          },
          {
            id: 2,
            operateCode: 'CZ202401002',
            projectName: '灌溉渠道工程',
            operateContent: '开启灌溉渠道进水闸',
            cmdCode: 'DD202401002',
            cmdName: '夏季灌溉调度指令',
            operateName: '王五',
            guardianName: '赵六',
            operateTime: '2024-06-20 06:15 - 2024-06-20 06:45',
            startDate: '2024-06-20 06:15:00',
            endDate: '2024-06-20 06:45:00',
            operateDate: '2024-06-20',
            remark: '灌溉渠道开启正常，水流稳定',
            // 关联的调度指令详情
            dispatchDetail: {
              id: 2,
              cmdCode: 'DD202401002',
              cmdName: '夏季灌溉调度指令',
              dispatchCode: '灌溉调度方案B',
              planWorkTime: '2024-06-20 06:00 ~ 2024-06-20 20:00',
              workManager: '王五',
              status: 3,
              projectList: [
                {
                  projectName: '灌溉渠道工程',
                  manager: '赵六',
                  openTime: '2024-06-20 06:00',
                  closeTime: '2024-06-20 20:00',
                  remark: '确保水量充足'
                }
              ]
            },
            // 操作项目详情
            operateDetails: [
              { content: '检查渠道水位', status: 1 },
              { content: '清理渠道杂物', status: 1 },
              { content: '开启进水闸门', status: 1 },
              { content: '调节水流量', status: 1 },
              { content: '监测下游水位', status: 1 }
            ]
          }
        ],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '操作令编号',
            field: 'operateCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '操作内容',
            field: 'operateContent',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '关联的调度指令',
            field: 'cmdCode',
            minWidth: 140,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDispatchDetail(row)} style="color: #1890ff;">
                      {row.cmdCode}
                    </a>
                  </span>
                )
              },
            },
          },
          {
            title: '操作人',
            field: 'operateName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '监护人',
            field: 'guardianName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作时间',
            field: 'operateTime',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleOptDetail(row)}>查看</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    computed: {
      // 过滤后的数据
      filteredData() {
        return this.mockData.filter(item => {
          const matchOperateCode = !this.queryParam.operateCode || item.operateCode.includes(this.queryParam.operateCode)
          const matchCmdCode = !this.queryParam.cmdCode || item.cmdCode.includes(this.queryParam.cmdCode)

          let matchDate = true
          if (this.queryParam.startDate && this.queryParam.endDate) {
            const itemDate = moment(item.operateDate)
            const startDate = moment(this.queryParam.startDate)
            const endDate = moment(this.queryParam.endDate)
            matchDate = itemDate.isBetween(startDate, endDate, 'day', '[]')
          }

          return matchOperateCode && matchCmdCode && matchDate
        })
      }
    },
    methods: {
      onRangeChange(value, dateString) {
        if (dateString.length == 0) {
          this.queryParam.startDate = null
          this.queryParam.endDate = null
          return
        }
        this.queryParam.startDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },

      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.dispatchShowForm = false
        this.loading = true
        this.selectChange({ records: [] })

        // 模拟异步请求
        setTimeout(() => {
          const filtered = this.filteredData
          const start = (this.queryParam.pageNum - 1) * this.queryParam.pageSize
          const end = start + this.queryParam.pageSize
          this.list = filtered.slice(start, end)
          this.total = filtered.length
          this.loading = false
        }, 500)
      },

      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        this.isChecked = !!valObj.records.length
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          operateCode: '',
          cmdCode: '',
          startDate: null,
          endDate: null,
          optTime: [],
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 查看调度指令详情
      handleDispatchDetail(record) {
        this.dispatchShowForm = true
        this.$nextTick(() => this.$refs.dispatchFormRef.handle(record.dispatchDetail, 'view'))
      },

      // 查看操作指令详情
      handleOptDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
    },
  }
</script>

<style lang="less" scoped>
</style>