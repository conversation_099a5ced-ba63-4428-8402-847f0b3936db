<template>
  <div class="common-editor">
    <codemirror ref="myCodemirror" v-model="newCode" :options="coderOptions" v-bind="$attrs" v-on="$listeners" />
  </div>
</template>

<script lang="jsx">
  import { codemirror } from 'vue-codemirror'
  import 'codemirror/lib/codemirror.css'
  import 'codemirror/theme/base16-dark.css'
  import 'codemirror/theme/blackboard.css'
  import 'codemirror/theme/base16-light.css'
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/show-hint.js'
  import 'codemirror/addon/hint/show-hint'
  import 'codemirror/addon/hint/javascript-hint'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/mode/javascript/javascript'
  import 'codemirror/mode/markdown/markdown'
  import 'codemirror/mode/sql/sql'
  import 'codemirror/mode/php/php'
  import 'codemirror/mode/python/python'
  import 'codemirror/mode/shell/shell'
  import 'codemirror/mode/powershell/powershell'
  import 'codemirror/mode/swift/swift'
  import 'codemirror/mode/vue/vue'
  import 'codemirror/mode/velocity/velocity'
  // 支持括号自动匹配
  import 'codemirror/addon/edit/closebrackets.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  // 引入自动刷新
  import 'codemirror/addon/display/autorefresh'

  export default {
    name: 'CommonEditor',
    components: { codemirror },
    props: {
      value: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        newCode: null,
        newValue: null
      }
    },
    watch: {
      newCode: {
        handler(newVal, oldVal) {
          this.newValue = newVal
        },
        immediate: true
      }
    },
    computed: {
      coderOptions() {
        return {
          line: true,
          mode: 'text/velocity', // json数据高亮
          theme: 'base16-light', //设置主题 记得引入对应主题才有显示   import 'codemirror/theme/blackboard.css'
          lineNumbers: true, // 显示行号
          tabSize: 2, //tab字符的宽度，默认为4
          cursorHeight: 0.8, //光标高度，默认是1
          autoCloseBrackets: true,
          matchBrackets: true, // 括号匹配
          lineWrapping: 'wrap', // 文字过长时，是换行(wrap)还是滚动(scroll),默认是滚动
          showCursorWhenSelecting: true, // 文本选中时显示光标
          autoRefresh: true, // 自动触发刷新
          completeSingle: true // 当匹配只有一项的时候是否自动补全
        }
      }
    },
    mounted() {
      this.initialize() // 初始化
    },
    methods: {
      initialize() {
        this.newCode = this.value
        setTimeout(() => {
          this.newCode = this.value
          // 设置触发刷新
          this.$refs.myCodemirror.refresh()
        }, 100)
      },
      changeValue() {
        this.$emit('change', this.newValue)
      }
    }
  }
</script>
<style lang="less">
  .common-editor {
    width: 100%;
    height: 100%;
    .CodeMirror {
      line-height: 20px;
      width: 100%;
      // height: 100%;
    }

    .custom-class .CodeMirror {
      width: 100%;
    }
    .CodeMirror-scroll {
      height: 86% !important;
    }
    .CodeMirror-sizer {
      height: 330px !important;
    }
  }
  .CodeMirror-gutters {
    left: 0px !important;
  }
  .CodeMirror-hints {
    z-index: 1000;
  }
</style>
