<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="调度令编号">
        <a-input v-model="queryParam.cmdCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="调度令名称">
        <a-input v-model="queryParam.cmdName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="调度方案">
        <a-select
          show-search
          allow-clear
          v-model="queryParam.dispatchId"
          :options="dispatchOptions"
          placeholder="请选择"
          option-filter-prop="children"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()" v-if="hasAddPermission">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          @ok="onOperationComplete"
          @close="showForm = false"
          :dispatchOptions="dispatchOptions"
        />
        <AuditModal
          v-if="showAudit"
          ref="auditRef"
          @close="showAudit = false"
          @auditResult="handleAuditResult"
        />
        <DetailModal
          v-if="showDetail"
          ref="detailRef"
          @close="showDetail = false"
          @receiveResult="handleReceiveResult"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import FormDrawer from './modules/FormDrawer.vue'
  import AuditModal from './modules/AuditModal.vue'
  import DetailModal from './modules/DetailModal.vue'
  import ExpandTable from './modules/ExpandTable.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'DispatchInstruction',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
      AuditModal,
      DetailModal,
      ExpandTable,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: Number,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        isChecked: false,
        showForm: false,
        showAudit: false,
        showDetail: false,
        dispatchOptions: [
          { label: '防汛调度方案A', value: 1 },
          { label: '灌溉调度方案B', value: 2 },
          { label: '生态调水方案C', value: 3 },
        ],
        list: [],
        tableTitle: '调度指令',
        loading: false,
        total: 0,
        selectIds: [],
        hasAddPermission: true,
        queryParam: {
          dispatchId: null,
          cmdName: '',
          cmdCode: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        // 测试数据
        mockData: [
          {
            id: 1,
            cmdCode: 'DD202401001',
            cmdName: '春季防汛调度指令',
            dispatchCode: '防汛调度方案A',
            dispatchId: 1,
            planWorkTime: '2024-03-15 08:00 ~ 2024-03-15 18:00',
            workManager: '张三',
            status: 0, // 0 审批中 1 下发中 2 已驳回 3 已完成
            createTime: '2024-03-10 10:30:00',
            projectList: [
              {
                projectName: '水库闸门工程',
                manager: '李四',
                openTime: '2024-03-15 08:00',
                closeTime: '2024-03-15 18:00',
                remark: '注意安全操作'
              }
            ]
          },
          {
            id: 2,
            cmdCode: 'DD202401002',
            cmdName: '夏季灌溉调度指令',
            dispatchCode: '灌溉调度方案B',
            dispatchId: 2,
            planWorkTime: '2024-06-20 06:00 ~ 2024-06-20 20:00',
            workManager: '王五',
            status: 1, // 0 审批中 1 下发中 2 已驳回 3 已完成
            createTime: '2024-06-15 09:15:00',
            projectList: [
              {
                projectName: '灌溉渠道工程',
                manager: '赵六',
                openTime: '2024-06-20 06:00',
                closeTime: '2024-06-20 20:00',
                remark: '确保水量充足'
              }
            ]
          }
        ],
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            type: 'expand',
            width: 30,
            fixed: 'left',
            slots: {
              content: ({ row, rowIndex }) => {
                return <ExpandTable row={row} />
              },
            },
          },
          {
            title: '调度令编号',
            field: 'cmdCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '调度令名称',
            field: 'cmdName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '调度方案',
            field: 'dispatchCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '计划工作时间',
            field: 'planWorkTime',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '工作负责人',
            field: 'workManager',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '当前状态',
            field: 'status',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                const statusMap = {
                  0: { text: '审批中', color: '#1890ff' },
                  1: { text: '下发中', color: '#52c41a' },
                  2: { text: '已驳回', color: '#ff4d4f' },
                  3: { text: '已完成', color: '#8c8c8c' }
                }
                const status = statusMap[row.status] || { text: '未知', color: '#8c8c8c' }
                return <span style={`color: ${status.color}`}>{status.text}</span>
              },
            },
          },
          {
            title: '操作',
            field: 'action',
            width: 200,
            fixed: 'right',
            slots: {
              default: ({ row }) => {
                const buttons = []

                // 审核按钮（状态为0显示）
                if (row.status === 0) {
                  buttons.push(<a onClick={() => this.handleAudit(row)}>审核</a>)
                }

                // 接收按钮（状态为1显示）
                if (row.status === 1) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleReceive(row)}>接收</a>)
                }

                // 查看按钮（状态为2或3显示）
                if (row.status === 2 || row.status === 3) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleView(row)}>查看</a>)
                }

                // 修改按钮（状态为0或2显示）
                if (row.status === 0 || row.status === 2) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleEdit(row)}>修改</a>)
                }

                // 复制按钮（固定显示）
                if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                buttons.push(<a onClick={() => this.handleCopy(row)}>复制</a>)

                // 删除按钮（固定显示）
                buttons.push(<a-divider type='vertical' />)
                buttons.push(<a onClick={() => this.handleDeleteSingle(row)}>删除</a>)

                return <span>{buttons}</span>
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    computed: {
      // 过滤后的数据
      filteredData() {
        return this.mockData.filter(item => {
          const matchCode = !this.queryParam.cmdCode || item.cmdCode.includes(this.queryParam.cmdCode)
          const matchName = !this.queryParam.cmdName || item.cmdName.includes(this.queryParam.cmdName)
          const matchDispatch = !this.queryParam.dispatchId || item.dispatchId === this.queryParam.dispatchId
          return matchCode && matchName && matchDispatch
        })
      }
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showAudit = false
        this.showDetail = false
        this.loading = true
        this.selectChange({ records: [] })

        // 模拟异步请求
        setTimeout(() => {
          const filtered = this.filteredData
          const start = (this.queryParam.pageNum - 1) * this.queryParam.pageSize
          const end = start + this.queryParam.pageSize
          this.list = filtered.slice(start, end)
          this.total = filtered.length
          this.loading = false
        }, 500)
      },

      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        this.isChecked = !!valObj.records.length
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          dispatchId: null,
          cmdName: '',
          cmdCode: '',
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 自适应页面大小变化
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.queryParam.pageNum = 1
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showForm = true
        let row = { action: '新增' }
        this.$nextTick(() => this.$refs.formRef.handle(row))
      },

      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        record.action = '修改'
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },

      /* 复制 */
      handleCopy(record) {
        this.showForm = true
        record.action = '复制'
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },

      /* 审核 */
      handleAudit(record) {
        this.showAudit = true
        this.$nextTick(() => this.$refs.auditRef.handle(record, 'audit'))
      },

      /* 接收 */
      handleReceive(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'receive'))
      },

      /* 查看 */
      handleView(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'view'))
      },

      /* 删除单个 */
      handleDeleteSingle(record) {
        this.$confirm({
          title: '确认删除',
          content: `确定要删除调度令"${record.cmdName}"吗？`,
          onOk: () => {
            // 模拟删除操作
            const index = this.mockData.findIndex(item => item.id === record.id)
            if (index > -1) {
              this.mockData.splice(index, 1)
              this.$message.success('删除成功')
              this.getList()
            }
          }
        })
      },

      /* 批量删除 */
      handleDelete() {
        if (!this.selectIds.length) {
          this.$message.warning('请选择要删除的数据')
          return
        }
        this.$confirm({
          title: '确认删除',
          content: `确定要删除选中的 ${this.selectIds.length} 条记录吗？`,
          onOk: () => {
            // 模拟批量删除操作
            this.selectIds.forEach(id => {
              const index = this.mockData.findIndex(item => item.id === id)
              if (index > -1) {
                this.mockData.splice(index, 1)
              }
            })
            this.$message.success('删除成功')
            this.getList()
          }
        })
      },

      /* 操作完成回调 */
      onOperationComplete() {
        this.getList()
      },

      /* 审核结果回调 */
      handleAuditResult(record, auditForm) {
        // 更新记录状态
        const item = this.mockData.find(item => item.id === record.id)
        if (item) {
          item.status = auditForm.auditResult === 1 ? 1 : 2 // 1-下发中, 2-已驳回
        }
        this.$message.success('审核完成')
        this.getList()
      },

      /* 接收结果回调 */
      handleReceiveResult(record) {
        // 更新记录状态为已完成
        const item = this.mockData.find(item => item.id === record.id)
        if (item) {
          item.status = 3 // 3-已完成
        }
        this.$message.success('接收完成')
        this.getList()
      },
    }
  }
</script>

<style lang="less" scoped>
// .common-table-page {
//     width: 100%;
//   height: 100%;

//   :deep(.ant-form-item) {
//     margin-bottom: 16px;
//   }
// }
</style>