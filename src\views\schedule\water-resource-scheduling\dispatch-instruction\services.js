import request from '@/utils/request'

// 模拟测试数据
const mockData = [
  {
    id: 1,
    cmdCode: 'DD202401001',
    cmdName: '春季防汛调度指令',
    dispatchCode: '防汛调度方案A',
    dispatchId: 1,
    planWorkTime: '2024-03-15 08:00 ~ 2024-03-15 18:00',
    workManager: '张三',
    status: 0, // 0 审批中 1 下发中 2 已驳回 3 已完成
    createTime: '2024-03-10 10:30:00',
    projectList: [
      {
        projectName: '水库闸门工程',
        manager: '李四',
        openTime: '2024-03-15 08:00',
        closeTime: '2024-03-15 18:00',
        remark: '注意安全操作，确保闸门开启到位'
      },
      {
        projectName: '排水泵站工程',
        manager: '王五',
        openTime: '2024-03-15 09:00',
        closeTime: '2024-03-15 17:00',
        remark: '监控水位变化，及时调整泵站运行'
      }
    ]
  },
  {
    id: 2,
    cmdCode: 'DD202401002',
    cmdName: '夏季灌溉调度指令',
    dispatchCode: '灌溉调度方案B',
    dispatchId: 2,
    planWorkTime: '2024-06-20 06:00 ~ 2024-06-20 20:00',
    workManager: '王五',
    status: 1, // 0 审批中 1 下发中 2 已驳回 3 已完成
    createTime: '2024-06-15 09:15:00',
    projectList: [
      {
        projectName: '灌溉渠道工程',
        manager: '赵六',
        openTime: '2024-06-20 06:00',
        closeTime: '2024-06-20 20:00',
        remark: '确保水量充足，按时开启灌溉渠道'
      },
      {
        projectName: '提水泵站工程',
        manager: '钱七',
        openTime: '2024-06-20 06:30',
        closeTime: '2024-06-20 19:30',
        remark: '配合渠道工程，保证灌溉用水需求'
      }
    ]
  }
]

// 分页查询调度指令
export function getPage(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟分页查询
      const { pageNum = 1, pageSize = 10, cmdCode, cmdName, dispatchId } = data
      
      let filteredData = [...mockData]
      
      // 筛选条件
      if (cmdCode) {
        filteredData = filteredData.filter(item => item.cmdCode.includes(cmdCode))
      }
      if (cmdName) {
        filteredData = filteredData.filter(item => item.cmdName.includes(cmdName))
      }
      if (dispatchId) {
        filteredData = filteredData.filter(item => item.dispatchId === dispatchId)
      }
      
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)
      
      resolve({
        code: 200,
        data: {
          data: list,
          total: filteredData.length,
          pageNum,
          pageSize
        }
      })
    }, 500)
  })
}

// 删除调度指令
export function deleteDispatchInstruction(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '删除成功'
      })
    }, 300)
  })
}

// 新增调度指令
export function addDispatchInstruction(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟新增
      const newId = Math.max(...mockData.map(item => item.id)) + 1
      const newItem = {
        ...data,
        id: newId,
        status: 0, // 新增默认为审批中
        createTime: new Date().toLocaleString()
      }
      mockData.push(newItem)
      
      resolve({
        code: 200,
        data: newItem,
        msg: '新增成功'
      })
    }, 800)
  })
}

// 修改调度指令
export function editDispatchInstruction(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟修改
      const index = mockData.findIndex(item => item.id === data.id)
      if (index > -1) {
        mockData[index] = { ...mockData[index], ...data }
      }
      
      resolve({
        code: 200,
        data: mockData[index],
        msg: '修改成功'
      })
    }, 800)
  })
}

// 根据ID获取调度指令详情
export function getDispatchInstructionById(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.id === params.id)
      resolve({
        code: 200,
        data: item || {}
      })
    }, 300)
  })
}

// 审核调度指令
export function auditDispatchInstruction(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟审核
      const index = mockData.findIndex(item => item.id === data.id)
      if (index > -1) {
        mockData[index].status = data.auditResult === 1 ? 1 : 2 // 1-下发中, 2-已驳回
      }
      
      resolve({
        code: 200,
        msg: '审核成功'
      })
    }, 1000)
  })
}

// 接收调度指令
export function receiveDispatchInstruction(id) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟接收
      const index = mockData.findIndex(item => item.id === id)
      if (index > -1) {
        mockData[index].status = 3 // 3-已完成
      }
      
      resolve({
        code: 200,
        msg: '接收成功'
      })
    }, 1000)
  })
}

// 检查调度令编码是否存在
export function isDispatchCodeExist(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const exists = mockData.some(item => item.cmdCode === params.cmdCode)
      resolve({
        code: 200,
        data: exists
      })
    }, 500)
  })
}

// 获取调度方案选项
export function getDispatchOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { label: '防汛调度方案A', value: 1 },
          { label: '灌溉调度方案B', value: 2 },
          { label: '生态调水方案C', value: 3 },
        ]
      })
    }, 300)
  })
}

// 获取工程选项
export function getProjectOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { label: '水库闸门工程', value: 1 },
          { label: '灌溉渠道工程', value: 2 },
          { label: '排水泵站工程', value: 3 },
          { label: '提水泵站工程', value: 4 },
        ]
      })
    }, 300)
  })
}

// 获取用户选项
export function getUserOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { label: '张三', value: 1 },
          { label: '李四', value: 2 },
          { label: '王五', value: 3 },
          { label: '赵六', value: 4 },
          { label: '钱七', value: 5 },
        ]
      })
    }, 300)
  })
}

// 导出模拟数据供其他地方使用
export { mockData }
