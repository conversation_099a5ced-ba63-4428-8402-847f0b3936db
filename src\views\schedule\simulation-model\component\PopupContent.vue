<template>
  <div class="container">
    <div class="header">
      <!-- <div class="icon">{{ item?.ditch?.projectName.slice(0, 1) }}</div> -->
      <div class="name">{{ item?.projectName }}</div>
      <a-icon type="close" style="cursor: pointer" @click="item.onPopupClose(item)" />
    </div>

    <div class="indicator">
      <div class="label"><span style="color:#1d2129;">{{ "流量:"}}</span> {{ item?.flow  ? item?.flow + 'm3/s' : '暂无' }}</div>
      <div class="label"><span style="color:#1d2129;">{{ "上游水位:"}}</span>{{ item?.upWlv ? item?.upWlv + 'm' : '暂无' }}</div>
      <div class="label"><span style="color:#1d2129;">{{ "下游水位:"}}</span>{{ item?.downWlv ? item?.downWlv + 'm' : '暂无' }}</div>
    </div>
    <div style="text-align: center; margin-bottom: 14px;">
      <a-button type="primary" size="small" @click.stop="item.onProcessClick(item)">过程曲线</a-button>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PopupContent',
    props: ['item'],
    computed: {},
    mounted() {},
  }
</script>
<style lang="less">
  .mapboxgl-popup-content {
    padding: 0;
  }
</style>
<style lang="less" scoped>
  .container {
    width: 160px;
    max-height: 200px;
    position: relative;
    display: flex;
    flex-direction: column;
    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      align-items: center;
      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .name {
        flex: 1;
        margin: 0 13px 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicator {
      display: flex;
      flex-direction: column;
      padding: 4px 8px;
      .label {
        font-size: 10px;
        color: #4E5969;
      }
    }
  }
</style>
