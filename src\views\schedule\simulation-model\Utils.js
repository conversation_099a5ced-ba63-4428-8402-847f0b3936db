// 提取 水文监测站 时间轴 最大最小水位或流量值
export function extractData(modelResult) {
    let minWlevel = Infinity; // 初始化最小值为负无穷大
    let maxWlevel = -Infinity; // 初始化最大值为正无穷大
    let minQ = Infinity;
    let maxQ = -Infinity;

    const times = []  // 存储时间的数组
    const firstData = modelResult[Object.keys(modelResult)[0]]; // 获取第一个对象的 data 属性
    for (const time in firstData.data) {
        times.push(time)
    }

    // 遍历每个 hedaoId 的数据 找到所有数据中水位最大小、流量最大v值
    for (const hedaoId in modelResult) {
        const { data } = modelResult[hedaoId];
        let minWlevel1 = Infinity; // 初始化最小值为负无穷大
        let maxWlevel1 = -Infinity; // 初始化最大值为正无穷大
        let minQ1 = Infinity;
        let maxQ1 = -Infinity;
        for (const time in data) {
            const { wlevel, q } = data[time];
            wlevel.forEach((item, index) => {
                wlevel[index] = parseFloat(wlevel[index]).toFixed(3)
            });
            let tempMaxWlevel = Math.max(...wlevel); // 计算当前时间的最大值
            let tempMaxQ = Math.max(...q); // 计算当前时间的最大值
            let tempMinWlevel = Math.min(...wlevel); // 计算当前时间的最大值
            let tempMinQ = Math.min(...q); // 计算当前时间的最大值
            if (tempMaxWlevel > maxWlevel1) {
                maxWlevel1 = Math.ceil(tempMaxWlevel); // 更新最大值
            }
            if (tempMaxQ > maxQ1) {
                maxQ1 = Math.ceil(tempMaxQ); // 更新最大值         
            }
            if (tempMinWlevel < minWlevel1) {
                minWlevel1 = Math.floor(tempMinWlevel); // 更新最大值
            }
            if (tempMinQ < minQ1) {
                minQ1 = Math.floor(tempMinQ); // 更新最大值
            }
        }
        
        
        modelResult[hedaoId]['maxWlevel'] = maxWlevel1; // 存储最大值
        modelResult[hedaoId]['minWlevel'] = minWlevel1; // 存储最大值
        modelResult[hedaoId]['maxQ'] = maxQ1; // 存储最大值
        modelResult[hedaoId]['minQ'] = minQ1; // 存储最大值
        if (maxWlevel1 > maxWlevel) {
            maxWlevel = maxWlevel1; // 更新最大值
        }
        if (maxQ1 > maxQ) {
            maxQ = maxQ1; // 更新最大值         
        }
        if (minWlevel1 < minWlevel) {
            minWlevel = minWlevel1; // 更新最大值
        }
        if (minQ1 < minQ) {
            minQ = minQ1; // 更新最大值
        }
    }
    return { times, maxWlevel, maxQ, minWlevel, minQ };
}

export function getChartsData(lineid, time, modelResult) {
    if (!(lineid in modelResult)) {
        return {
            wlevel: [],
            q: [],
            stakes: []
        }
    }
    const { data, stakes, maxWlevel, minWlevel, maxQ, minQ } = modelResult[lineid];
    const { wlevel, q } = data[time+":00"];
    return { wlevel, q, stakes, maxWlevel, minWlevel, maxQ, minQ }
}




