<template>
  <div class="container" style="height: 100%; padding: 24px">
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 16 },
      }"
      style="max-width: 600px"
    >
      <a-form-model-item label="调度方案名称" prop="caseName">
        <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
      </a-form-model-item>
      
      <a-form-model-item label="来水方案" prop="waterFcstId">
        <a-select
          v-model="form.waterFcstId"
          placeholder="请选择来水方案"
          show-search
          option-filter-prop="children"
          :options="waterFcstOptions"
          @change="changeWaterFcst"
        />
      </a-form-model-item>
      
      <a-form-model-item label="调度方式" prop="dispatchMethod">
        <a-radio-group
          v-model="form.dispatchMethod"
          style="margin-top: 5px"
          @change="changeDispatchMethod"
        >
          <a-radio v-for="(el, idx) in dispatchMethodOptions" :key="idx" :value="el.value">
            {{ el.label }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      
      <a-form-model-item label="模型应用场景" prop="scene">
        <a-radio-group
          v-model="form.scene"
          style="margin-top: 5px"
          @change="changeScene"
        >
          <a-radio v-for="(el, idx) in sceneOptions" :key="idx" :value="el.value">
            {{ el.label }}场景
            <!-- <a-tooltip v-if="el.value === 2">
              <template slot="title">可对未来72h的数据进行预报模拟</template>
              ⓘ
            </a-tooltip> -->
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      
      <a-form-model-item label="调度时间" prop="dispatchTime">
        <div style="display: flex; gap: 8px; align-items: center;">
          <a-select
            v-model="form.dispatchTimeType"
            style="width: 120px;"
            :disabled="form.scene === 1"
            @change="handleDispatchTimeTypeChange"
          >
            <a-select-option value="1" :disabled="form.scene === 1">未来1天</a-select-option>
            <a-select-option value="3" :disabled="form.scene === 1">未来3天</a-select-option>
            <a-select-option value="7" :disabled="form.scene === 1">未来7天</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
          <!-- 预设模式：显示禁用的开始时间和结束时间 -->
          <template v-if="form.dispatchTimeType !== 'custom'">
            <a-date-picker
              v-model="form.presetStartTime"
              disabled
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              placeholder="开始时间"
              style="flex: 1;"
            />
            <span style="margin: 0 4px;">至</span>
            <a-date-picker
              v-model="form.presetEndTime"
              disabled
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              placeholder="结束时间"
              style="flex: 1;"
            />
          </template>
          <!-- 自定义模式：显示可编辑的开始时间和结束时间 -->
          <template v-else>
            <a-date-picker
              v-model="form.customStartTime"
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              :disabled-date="disabledCustomStartDate"
              placeholder="开始时间"
              style="flex: 1;"
              @change="handleCustomStartTimeChange"
            />
            <span style="margin: 0 4px;">至</span>
            <a-date-picker
              v-model="form.customEndTime"
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              :disabled-date="disabledCustomEndDate"
              placeholder="结束时间"
              style="flex: 1;"
              @change="handleCustomEndTimeChange"
            />
          </template>
        </div>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
  import moment from 'moment'
  import { getInWaterPage } from '../../services'
  import { sceneOptions } from '../../config'

  // 调度方式选项
  const dispatchMethodOptions = [
    { label: '现状调度', value: 1 },
    { label: '推荐调度', value: 2 },
    { label: '手动调度', value: 3 },
  ]

  export default {
    name: 'BasicInfo',
    props: ['inWaterEchoData', 'tableData'],
    data() {
      return {
        waterFcstOptions: [],
        dispatchMethodOptions,
        sceneOptions,
        form: {
          caseName: undefined,
          waterFcstId: this.inWaterEchoData?.inWaterId || undefined,
          dispatchMethod: 2,
          scene: this.inWaterEchoData?.scene || 2, // 默认选择未来预报场景
          dispatchTimeType: '3', // 默认选择未来3天
          presetStartTime: undefined, // 预设模式的开始时间
          presetEndTime: undefined, // 预设模式的结束时间
          customStartTime: undefined, // 自定义模式的开始时间
          customEndTime: undefined, // 自定义模式的结束时间
          startTime: this.inWaterEchoData?.startTime || undefined, // 保留用于向后兼容
          endTime: this.inWaterEchoData?.endTime || undefined, // 保留用于向后兼容
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          waterFcstId: [{ required: true, message: '来水方案不能为空', trigger: 'change' }],
          dispatchMethod: [{ required: true, message: '调度方式不能为空', trigger: 'change' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          dispatchTime: [
            {
              validator: (rule, value, callback) => {
                if (this.form.dispatchTimeType === 'custom') {
                  if (!this.form.customStartTime || !this.form.customEndTime) {
                    callback(new Error('请选择完整的时间区间'))
                  } else {
                    // 校验结束时间必须晚于开始时间
                    const startTime = moment(this.form.customStartTime)
                    const endTime = moment(this.form.customEndTime)
                    if (endTime.isSameOrBefore(startTime)) {
                      callback(new Error('结束时间必须晚于开始时间'))
                    } else {
                      callback()
                    }
                  }
                } else {
                  if (!this.form.presetStartTime || !this.form.presetEndTime) {
                    callback(new Error('调度时间不能为空'))
                  } else {
                    callback()
                  }
                }
              },
              trigger: 'change'
            }
          ],
        },
      }
    },
    created() {
      // 调用来水方案接口
      getInWaterPage({ pageNum: 1, pageSize: 10 }).then(res => {
        if (res.success && res.data?.data) {
          this.waterFcstOptions = res.data.data.map(el => ({
            ...el,
            label: el.caseName,
            value: el.inWaterId
          }))

          // 如果没有预设的来水方案，自动选择第一条
          if (!this.form.waterFcstId && this.waterFcstOptions.length > 0) {
            this.form.waterFcstId = this.waterFcstOptions[0].value
            // 触发来水方案变更事件，自动填充相关字段（标记为自动选择，不覆盖默认场景）
            this.changeWaterFcst(this.form.waterFcstId, true)
          }
        }
      }).catch(err => {
        console.error('获取来水方案失败:', err)
        this.$message.error('获取来水方案失败')

        // 失败时使用模拟数据
        this.waterFcstOptions = [
          { label: '来水方案1', value: 1, scene: 1, startTime: '2024-01-01 08:00', endTime: '2024-01-03 20:00' },
          { label: '来水方案2', value: 2, scene: 2, startTime: '2024-01-02 08:00', endTime: '2024-01-04 20:00' },
          { label: '来水方案3', value: 3, scene: 1, startTime: '2024-01-03 08:00', endTime: '2024-01-05 20:00' },
        ]

        if (!this.form.waterFcstId && this.waterFcstOptions.length > 0) {
          this.form.waterFcstId = this.waterFcstOptions[0].value
          this.changeWaterFcst(this.form.waterFcstId, true)
        }
      })
    },
    mounted() {
      // 根据场景初始化调度时间
      if (this.form.scene === 2) {
        // 未来预报场景：使用预设模式
        this.initializeDispatchTime()
      } else if (this.form.scene === 1) {
        // 历史复演场景：强制使用自定义模式
        this.form.dispatchTimeType = 'custom'
        this.initializeCustomTime()
      }
    },
    methods: {
      generateCaseName() {
        if (this.form.scene !== 2) {
          this.form.caseName = undefined
          return
        }

        const isCustom = this.form.dispatchTimeType === 'custom'
        const startTimeString = isCustom ? this.form.customStartTime : this.form.presetStartTime

        if (!startTimeString) {
          return
        }

        const startTime = moment(startTimeString, 'YYYY-MM-DD HH:00')
        const startTimeFormattedForName = startTime.format('YYYYMMDDHHmm')

        const sameTimeCount = (this.tableData || []).filter(item => item.startTime === startTimeString).length

        const sequence = (sameTimeCount + 1).toString().padStart(2, '0')

        this.form.caseName = `水库调度${startTimeFormattedForName}${sequence}`
      },
      changeWaterFcst(val, isAutoSelect = false) {
        const obj = this.waterFcstOptions.find(el => el.value === val)

        // 如果是自动选择且已有默认场景值，则不覆盖场景
        if (!isAutoSelect || !this.form.scene) {
          this.form.scene = obj?.scene
        }

        // 如果是自动选择且当前场景是未来预报(2)，则不覆盖时间，让mounted中的逻辑处理
        if (!isAutoSelect || this.form.scene !== 2) {
          this.form.startTime = obj?.startTime
          this.form.endTime = obj?.endTime
        }

        // 存储选中的来水方案ID，用于后续接口调用
        this.form.inWaterId = val

        this.$nextTick(() => {
          this.$refs.form.validateField('scene')
          this.$refs.form.validateField('startTime')
          this.$refs.form.validateField('endTime')
        })
      },

      changeDispatchMethod() {
        // 调度方式改变时的处理逻辑
      },

      changeScene(val) {
        if (val.target.value === 2) {
          // 未来预报场景：恢复默认的未来3天模式
          this.form.dispatchTimeType = '3'
          this.initializeDispatchTime()
          this.$refs.form.validateField('dispatchTime')
        } else {
          // 历史复演场景：强制设置为自定义模式
          this.form.caseName = undefined
          this.form.dispatchTimeType = 'custom'
          this.form.presetStartTime = undefined
          this.form.presetEndTime = undefined
          this.initializeCustomTime()
          this.$refs.form.validateField('dispatchTime')
        }
      },

      // 初始化调度时间
      initializeDispatchTime() {
        // 开始时间为当前时间的下一个整点
        const now = moment()
        const startTime = now.clone().add(1, 'hours').startOf('hour')

        let endTime
        const days = parseInt(this.form.dispatchTimeType) || 3

        // 结束时间 = 开始时间 + 指定天数，但保持相同的小时
        endTime = startTime.clone().add(days, 'days').subtract(1, 'hours')

        this.form.presetStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.presetEndTime = endTime.format('YYYY-MM-DD HH:00')

        // 同时更新startTime和endTime用于向后兼容
        this.form.startTime = this.form.presetStartTime
        this.form.endTime = this.form.presetEndTime
        this.generateCaseName()
      },

      // 处理调度时间类型变化
      handleDispatchTimeTypeChange(value) {
        if (value !== 'custom') {
          // 切换到预设模式，清空自定义时间并初始化预设时间
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
          this.initializeDispatchTime()
        } else {
          // 切换到自定义模式，清空预设时间并初始化自定义时间
          this.form.presetStartTime = undefined
          this.form.presetEndTime = undefined
          this.initializeCustomTime()
        }
        this.$refs.form.validateField('dispatchTime')
      },

      // 初始化自定义时间
      initializeCustomTime() {
        const now = moment()

        if (this.form.scene === 1) {
          // 历史复演场景：默认时间为空
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
        } else {
          // 未来预报场景：开始时间为当前时间的下一个整点
          const startTime = now.clone().add(1, 'hours').startOf('hour')
          const endTime = startTime.clone().add(3, 'days').subtract(1, 'hours')

          this.form.customStartTime = startTime.format('YYYY-MM-DD HH:00')
          this.form.customEndTime = endTime.format('YYYY-MM-DD HH:00')
        }

        // 同时更新startTime和endTime用于向后兼容
        this.form.startTime = this.form.customStartTime
        this.form.endTime = this.form.customEndTime
        this.generateCaseName()
      },

      // 处理自定义开始时间变化
      handleCustomStartTimeChange() {
        // 当开始时间变化时，重新验证时间区间
        this.$nextTick(() => {
          this.$refs.form.validateField('dispatchTime')
        })
      },

      // 处理自定义结束时间变化
      handleCustomEndTimeChange() {
        // 当结束时间变化时，重新验证时间区间
        this.$nextTick(() => {
          this.$refs.form.validateField('dispatchTime')
        })
      },

      // 自定义开始时间日期禁用逻辑
      disabledCustomStartDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：只能选择今天之前的日期
          return current >= moment().startOf('day')
        } else {
          // 未来预报场景：可选择今天到未来10天（不限制结束时间，在验证时检查）
          return (
            current < moment().startOf('day') ||
            current > moment().add(10, 'days').endOf('day')
          )
        }
      },

      // 自定义结束时间日期禁用逻辑
      disabledCustomEndDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：只能选择今天之前的日期
          return current >= moment().startOf('day')
        } else {
          // 未来预报场景：可选择今天到未来10天（不限制开始时间，在验证时检查）
          return (
            current < moment().startOf('day') ||
            current > moment().add(10, 'days').endOf('day')
          )
        }
      },

      disabledStartDate(current) {
        if (this.form.scene === 1) {
          if (this.form.endTime) {
            return (
              current < moment(this.form.endTime).subtract(15, 'days') ||
              current > moment(this.form.endTime) ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          return current < moment().subtract(1, 'days')
        }
      },
      
      disabledEndDate(current) {
        if (this.form.scene === 1) {
          if (this.form.startTime) {
            return (
              current < moment(this.form.startTime) ||
              current > moment(this.form.startTime).add(15, 'days') ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          return current < moment().subtract(1, 'days') || current > moment().add(3, 'days')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            let startTime, endTime

            if (this.form.dispatchTimeType === 'custom') {
              // 自定义模式：使用用户选择的开始时间和结束时间
              startTime = this.form.customStartTime
              endTime = this.form.customEndTime
            } else {
              // 预设模式：使用计算出的开始时间和结束时间
              startTime = this.form.presetStartTime
              endTime = this.form.presetEndTime
            }

            // 构造传递给下一步的数据
            const formData = {
              ...this.form,
              startTime,
              endTime,
              // 确保传递inWaterId
              inWaterId: this.form.waterFcstId,
              // 调度方式映射
              dispathType: this.form.dispatchMethod,
              // 添加调度时间区间信息
              dispatchTimeRange: {
                type: this.form.dispatchTimeType,
                startTime,
                endTime
              }
            }

            this.$emit('saveData', formData)
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>

