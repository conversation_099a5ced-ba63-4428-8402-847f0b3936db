import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/custom/runmd/page',
    method: 'post',
    data,
  })
}

// 增加
export function addRunmd(data) {
  return request({
    url: '/custom/runmd/add',
    method: 'post',
    data,
  })
}
// 详情
export function getRunmdById(params) {
  return request({
    url: '/custom/runmd/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editRunmd(data) {
  return request({
    url: '/custom/runmd/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteRunmd(params) {
  return request({
    url: '/custom/runmd/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getDeviceByProjectId(params) {
  return request({
    url: '/custom/runmd/getDeviceList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 审核
export function auditRunmd(data) {
  return request({
    url: '/custom/runmd/audit',
    method: 'post',
    data,
  })
}

// 接收
export function recRunmd(data) {
  return request({
    url: '/custom/runmd/rec',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  })
}

// 工作票编码是否存在
export function isRunmdCodeExist(params) {
  return request({
    url: '/custom/runmd/cmdCodeExist',
    method: 'get',
    params,
  })
}