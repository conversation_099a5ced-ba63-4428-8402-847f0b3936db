<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1500"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <!-- 方案信息表格 -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 10px; font-weight: bold;">方案信息对比</h3>
        <a-table
          :columns="infoColumns"
          :data-source="compareData"
          :pagination="false"
          size="small"
          bordered
        />
      </div>

      <!-- 图表对比区域 -->
      <div style="flex: 1; display: flex; flex-direction: column;">
        <h3 style="margin-bottom: 10px; font-weight: bold;">数据对比</h3>

        <!-- 第一行：雨量图表和水位图表 -->
        <div style="height: 250px; display: flex; gap: 10px; margin-bottom: 10px;">
          <div style="width: 50%;">
            <RainfallChart :chartData="rainfallChartData" />
          </div>
          <div style="width: 50%;">
            <WaterLevelChart :chartData="waterLevelChartData" />
          </div>
        </div>

        <!-- 第二行：入库流量图表和供水流量图表 -->
        <div style="height: 250px; display: flex; gap: 10px; margin-bottom: 10px;">
          <div style="width: 50%;">
            <InflowChart :chartData="inflowChartData" />
          </div>
          <div style="width: 50%;">
            <SupplyFlowChart :chartData="supplyFlowChartData" />
          </div>
        </div>

        <!-- 第三行：泄洪流量图表 -->
        <div style="height: 250px; display: flex; gap: 10px;">
          <div style="width: 100%;">
            <FloodFlowChart :chartData="floodFlowChartData" />
          </div>
        </div>
      </div>
    </div>
    
    <template slot="footer">
      <a-button @click="cancel">关闭</a-button>
    </template>
  </ant-modal>
</template>

<script>
import { getDispRes } from '../../services'
import AntModal from '@/components/pt/dialog/AntModal'
import RainfallChart from './RainfallChart.vue'
import WaterLevelChart from './WaterLevelChart.vue'
import InflowChart from './InflowChart.vue'
import SupplyFlowChart from './SupplyFlowChart.vue'
import FloodFlowChart from './FloodFlowChart.vue'

export default {
  name: 'CompareModal',
  components: {
    AntModal,
    RainfallChart,
    WaterLevelChart,
    InflowChart,
    SupplyFlowChart,
    FloodFlowChart,
  },
  data() {
    return {
      open: false,
      modalLoading: false,
      modalTitle: '方案对比',
      compareRecords: [], // 对比的两个方案记录
      compareData: [], // 方案信息对比数据
      rainfallChartData: [],
      waterLevelChartData: [],
      inflowChartData: [],
      supplyFlowChartData: [],
      floodFlowChartData: [],
      
      // 方案信息表格列定义
      infoColumns: [
        {
          title: '方案名',
          dataIndex: 'caseName',
          key: 'caseName',
          width: 200,
        },
        {
          title: '方案编码',
          dataIndex: 'caseCode',
          key: 'caseCode',
          width: 220,
        },
        {
          title: '预报时段',
          dataIndex: 'forecastPeriod',
          key: 'forecastPeriod',
          width: 300,
        },
        {
          title: '调度方式',
          dataIndex: 'dispatchMethod',
          key: 'dispatchMethod',
          width: 120,
        },
      ],
    }
  },
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.$emit('close')
    },
    
    // 显示对比弹窗
    async handleShow(records) {
      if (records.length !== 2) {
        this.$message.error('请选择两个方案进行对比')
        return
      }
      
      this.open = true
      this.modalLoading = true
      this.compareRecords = records
      
      try {
        // 获取两个方案的详细数据
        const [res1, res2] = await Promise.all([
          getDispRes({ resvrDispId: records[0].resvrDispId }),
          getDispRes({ resvrDispId: records[1].resvrDispId })
        ])
        
        const data1 = res1.data
        const data2 = res2.data
        
        // 构建方案信息对比数据
        this.buildCompareData(data1, data2)
        
        // 构建图表数据
        this.buildChartData(data1, data2)
        
        this.modalLoading = false
      } catch (error) {
        this.modalLoading = false
        this.$message.error('获取方案数据失败')
        console.error(error)
      }
    },
    
    // 构建方案信息对比数据
    buildCompareData(data1, data2) {
      this.compareData = [
        {
          key: 'scheme1',
          caseName: data1.caseName,
          caseCode: data1.caseCode,
          forecastPeriod: `${data1.startTime} - ${data1.endTime}`,
          dispatchMethod: this.getDispatchMethodName(data1.dispathType),
        },
        {
          key: 'scheme2',
          caseName: data2.caseName,
          caseCode: data2.caseCode,
          forecastPeriod: `${data2.startTime} - ${data2.endTime}`,
          dispatchMethod: this.getDispatchMethodName(data2.dispathType),
        },
      ]
    },
    
    // 获取调度方式名称
    getDispatchMethodName(type) {
      const typeMap = {
        1: '现状调度',
        2: '推荐调度',
        3: '手动调度'
      }
      return typeMap[type] || '未知'
    },
    
    // 构建图表数据
    buildChartData(data1, data2) {
      const list1 = data1.resvrDispResList || []
      const list2 = data2.resvrDispResList || []
      
      // 雨量数据（柱状图）
      this.rainfallChartData = this.buildRainfallData(list1, list2, data1.caseName, data2.caseName)
      
      // 水位数据（折线图）
      this.waterLevelChartData = this.buildLineData(list1, list2, data1.caseName, data2.caseName, 'wlv', '水位')
      
      // 入库流量数据（折线图）
      this.inflowChartData = this.buildLineData(list1, list2, data1.caseName, data2.caseName, 'inflow', '入库流量')
      
      // 供水流量数据（折线图）
      this.supplyFlowChartData = this.buildLineData(list1, list2, data1.caseName, data2.caseName, 'outflow', '供水流量')
      
      // 泄洪流量数据（折线图）
      this.floodFlowChartData = this.buildLineData(list1, list2, data1.caseName, data2.caseName, 'floodflow', '泄洪流量')
    },
    
    // 构建雨量数据（包含时段雨量和累计降雨量）
    buildRainfallData(list1, list2, name1, name2) {
      const rainData1 = {
        name: `${name1}-时段雨量`,
        data: list1.map(el => [el.tm, el.rain || 0]),
        type: 'bar'
      }
      const rainData2 = {
        name: `${name2}-时段雨量`,
        data: list2.map(el => [el.tm, el.rain || 0]),
        type: 'bar'
      }
      
      // 计算累计降雨量
      const sumRainData1 = {
        name: `${name1}-累计降雨量`,
        data: rainData1.data.map((el, idx) => {
          const sum = rainData1.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
          return [el[0], +sum.toFixed(1)]
        }),
        type: 'line'
      }
      const sumRainData2 = {
        name: `${name2}-累计降雨量`,
        data: rainData2.data.map((el, idx) => {
          const sum = rainData2.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
          return [el[0], +sum.toFixed(1)]
        }),
        type: 'line'
      }
      
      return [rainData1, rainData2, sumRainData1, sumRainData2]
    },
    
    // 构建折线图数据
    buildLineData(list1, list2, name1, name2, field, label) {
      return [
        {
          name: `${name1}-${label}`,
          data: list1.map(el => [el.tm, el[field] || 0]),
        },
        {
          name: `${name2}-${label}`,
          data: list2.map(el => [el.tm, el[field] || 0]),
        }
      ]
    },
  },
}
</script>

<style lang="less" scoped>
::v-deep .modal-content {
  height: 100%;
}

::v-deep .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

::v-deep .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}
</style>
