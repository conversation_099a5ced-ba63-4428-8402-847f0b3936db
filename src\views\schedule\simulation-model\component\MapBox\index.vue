<template>
  <div id="mapBox" ref="mapBoxRef"></div>
</template>

<script>
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import { clearSourceAndLayer } from '@/utils/mapUtils.js'
import {TianDiTu_Items} from './constant.js'
import getTianDiTuMapByConfig from './constant.js'
import { getValueByKey } from '@/api/common'

export default {
  name: 'MapBox',
  props: ['activeStyle'],
  mapIns: null,
  components: {},
  data() {
    return {
      center: null,
    }
  },
  computed: {},
  watch: {},
  created() { },
  mounted() {
    getValueByKey('patrolCentralPoint').then(res => {
      this.center = res.data.split(',').map(el => +el)
      this.$nextTick(() => {
        this.createMap()
      })
    })
  },
  methods: {
    createMap() {
      mapboxgl.accessToken = 'pk.eyJ1IjoiaGhjY2RldiIsImEiOiJjbTBxaDBhdmswYzZjMmpwdzE4eWU2d2NvIn0._XkHfjxUcOLIZ7bIJUcbWw'
      this.mapIns = new mapboxgl.Map({
        container: "mapBox",
        style: {
          version: 8,
          sources: {},
          sprite: 'mapbox://sprites/mapbox/streets-v8',
          glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
          layers: [
            {
              id: 'background',
              type: 'background',
              paint: {
                'background-color': 'transparent',
              },
              interactive: true,
            },
          ],
        },
        center: this.center,
        zoom: 10,
        maxZoom: 17.49, // 天地图大于这个值时，图层会消失
        // minZoom: 4,
        pitch: 0, // 相对于地面3D视角的角度
        dragRotate: false,
        touchRotate: false,
        antialias: false, //抗锯齿，通过false关闭提升性能
        // maxBounds: [
        //   [73.66, 3.86],
        //   [135.05, 53.55],
        // ],
        ...this.$attrs?.options,
      })
      // ### 添加导航控制条
      // this.mapIns.addControl(new mapboxgl.NavigationControl({ showCompass: false }), 'top-left')
      // 鼠标样式
      this.mapIns.getCanvas().style.cursor = 'auto'
      this.mapIns.once('load', () => {
        this.$listeners?.onMapMounted && this.$listeners?.onMapMounted(this.mapIns)
        this.addTianDiTuLayer(this.activeStyle || "卫星图")
      })

      this.mapIns.on('style.load', () => {
        this.$listeners?.onMapStyleLoad && this.$listeners?.onMapStyleLoad(this.mapIns)
      })
    },
    addTianDiTuLayer(type) {
      clearSourceAndLayer(this.mapIns, ['mapbox-wmts-base-layer','mapbox-wmts-base-layer1'], ['mapbox-wmts-base-layer','mapbox-wmts-base-layer1'])
      getTianDiTuMapByConfig(type).forEach(element => {
        this.mapIns.addLayer({...element})
      });
    }
  },
}
</script>
<style lang="less" scoped>
#mapBox {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
