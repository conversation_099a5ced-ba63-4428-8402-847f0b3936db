[根目录](../../CLAUDE.md) > [src](../) > **components**

# 共享组件库架构文档

## 变更记录 (Changelog)

### 2025-09-01 13:58:17
- **[初始化]** 完成共享组件库架构分析
- **[发现]** 识别出25+个业务组件和工具组件

## 模块职责

共享组件库为整个水利系统提供可复用的 UI 组件和业务组件，提升开发效率和界面一致性。主要分类：
- **业务组件**: 针对水利行业特定需求的专业组件
- **工具组件**: 通用功能组件，如文件预览、数据展示等
- **表单组件**: 增强型表单控件和数据录入组件
- **可视化组件**: 地图、图表、视频播放等可视化组件

## 入口与启动

### 组件注册
全局组件通过 `src/components/index.js` 统一注册：
```javascript
// 自动注册全局组件
Vue.component('TableCellOverflow', TableCellOverflow)
Vue.component('SvgIcon', SvgIcon)
```

### 按需引入
业务页面可按需引入具体组件：
```javascript
import MapBox from '@/components/MapBox'
import VxeTable from '@/components/VxeTable'
```

## 对外接口

### 核心组件 API

#### MapBox 地图组件
```javascript
// 基础用法
<MapBox 
  :center="[120.123, 30.456]"
  :zoom="10"
  :layers="mapLayers"
  @click="handleMapClick"
/>
```

#### VxeTable 表格组件
```javascript
// 高性能表格
<VxeTable
  :data="tableData"
  :columns="columns"
  :loading="loading"
  @selection-change="handleSelect"
/>
```

#### ECharts 图表组件
```javascript
// 图表展示
<LineEchart
  :data="chartData"
  :options="chartOptions"
  height="400px"
/>
```

## 关键依赖与配置

### 地图组件依赖
```json
{
  "mapbox-gl": "^3.9.2",
  "@deck.gl/core": "^9.0.38",
  "@deck.gl/layers": "^9.0.38",
  "@mapbox/mapbox-gl-draw": "^1.4.3"
}
```

### 表格组件依赖
```json
{
  "vxe-table": "^3.15.29",
  "vxe-pc-ui": "^3.6.9"
}
```

### 视频组件依赖  
```json
{
  "video.js": "^8.6.0",
  "@easydarwin/easyplayer": "^5.1.1",
  "flv.js": "^1.6.2"
}
```

## 数据模型

### 地图组件数据结构
```javascript
// 地图图层配置
{
  id: 'layer-id',
  type: 'fill|line|circle|symbol',
  source: 'source-id',
  paint: Object,
  layout: Object,
  filter: Array
}
```

### 表格组件配置
```javascript
// 表格列配置
{
  field: 'fieldName',
  title: '列标题',
  width: Number,
  type: 'string|number|date',
  formatter: Function,
  slots: Object
}
```

## 测试与质量

### 组件测试策略
- **单元测试**: 针对每个组件的基本功能测试
- **快照测试**: 确保组件渲染结果的一致性
- **交互测试**: 验证用户交互和事件处理
- **性能测试**: 大数据量场景下的性能表现

### 质量保证
- Props 类型验证和默认值设置
- 详细的组件文档和使用示例
- 统一的错误处理和异常边界

## 常见问题 (FAQ)

### Q: 如何扩展 MapBox 组件的功能？
A: 通过 slots 和 events 扩展，支持自定义图层和交互逻辑，可参考 `src/views/project/hydraulic-project/modules/MapModel.vue` 示例。

### Q: VxeTable 组件如何处理大数据量？
A: 内置虚拟滚动功能，支持万级数据展示，配合分页和数据懒加载实现最佳性能。

### Q: 视频组件支持哪些格式？
A: 支持 MP4、FLV、HLS 等主流格式，集成了 EasyPlayer 和 Video.js 双引擎。

## 相关文件清单

### 业务组件
```
src/components/
├── MapBox/                    # 地图组件
│   └── index.vue
├── VxeTable/                  # 表格组件
│   └── index.vue
├── Echarts/                   # 图表组件
│   └── LineEchart.vue
├── EasyPlayer/                # 视频播放器
│   └── index.vue
└── TinyMCE/                   # 富文本编辑器
    └── index.vue
```

### 工具组件
```
src/components/
├── SvgIcon/                   # SVG 图标
├── TableCellOverflow/         # 表格溢出处理
├── UploadFile/                # 文件上传
├── PreviewWord/               # Word 预览
├── PreviewExcel/              # Excel 预览
├── PreviewTxt/                # 文本预览
└── Progress/                  # 进度条
```

### 表单组件
```
src/components/
├── TreeTransfer/              # 树形穿梭框
├── PermissionTransfer/        # 权限穿梭框
├── ProjectTransfer/           # 项目穿梭框
├── QuarterPicker/             # 季度选择器
├── TimePlaySlider/            # 时间播放滑块
└── VxeTableForm/              # 表格表单
```

### 业务特定组件
```
src/components/
├── TreeOrg/                   # 组织树
├── TreeGeneral/               # 通用树组件
├── TreePatrolCategory/        # 巡检分类树
├── MyRelation/                # 关系图
├── MonitoringIndex/           # 监测指标
├── DropdownTable/             # 下拉表格
└── Codemirror/                # 代码编辑器
```