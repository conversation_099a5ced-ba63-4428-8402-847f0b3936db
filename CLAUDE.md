# 河海水利基线前端项目架构文档

## 变更记录 (Changelog)

### 2025-09-01 13:58:17
- **[初始化]** 通过自适应架构师工具完成项目初始化分析
- **[新增]** 生成完整的项目架构文档与导航结构
- **[发现]** 识别出基于 Vue 2.7 + Ant Design Vue 的水利信息化管理系统

## 项目愿景

河海水利基线前端项目是一个基于 Vue.js 2.7 的综合性水利信息化管理系统，致力于为水利工程建设、运行调度、监测预警、档案管理等提供一站式的数字化解决方案。系统集成了 GIS 地图、实时监控、数据可视化、智能调度等核心功能模块。

## 架构总览

### 技术栈概览
- **前端框架**: Vue 2.7.16 + Vue Router 3.6.5 + Vuex 3.6.2
- **UI 组件库**: Ant Design Vue 1.7.8 + VXE Table 3.15.29
- **构建工具**: RSbuild 1.3.22 (替代 Webpack)
- **样式预处理**: Less + CSS Modules
- **地图渲染**: MapBox GL JS 3.9.2 + Deck.gl 9.0.38
- **图表可视化**: ECharts 5.5.1
- **视频播放**: Video.js 8.6.0 + EasyPlayer 5.1.1
- **文档处理**: TinyMCE 5.4.1 + xlsx
- **开发语言**: JavaScript ES6+ + JSX

### 模块结构图

```mermaid
graph TD
    A["河海水利基线前端项目"] --> B["src/"];
    B --> C["views/ (业务视图)"];
    B --> D["components/ (共享组件)"];
    B --> E["api/ (接口层)"];
    B --> F["store/ (状态管理)"];
    B --> G["router/ (路由配置)"];
    B --> H["utils/ (工具函数)"];
    B --> I["assets/ (静态资源)"];
    B --> J["layouts/ (布局组件)"];
    
    C --> C1["schedule/ (调度管理)"];
    C --> C2["water-rain/ (水雨情)"];
    C --> C3["early-warning/ (预警系统)"];
    C --> C4["project/ (工程管理)"];
    C --> C5["basic/ (基础配置)"];
    C --> C6["office/ (办公应用)"];
    C --> C7["archives/ (档案管理)"];
    C --> C8["equipment/ (设备管理)"];
    C --> C9["video/ (视频监控)"];
    C --> C10["briefing/ (简报系统)"];

    click C1 "./src/views/schedule/CLAUDE.md" "查看调度管理模块文档"
    click C2 "./src/views/water-rain/CLAUDE.md" "查看水雨情模块文档"
    click C3 "./src/views/early-warning/CLAUDE.md" "查看预警系统模块文档"
    click C4 "./src/views/project/CLAUDE.md" "查看工程管理模块文档"
    click C5 "./src/views/basic/CLAUDE.md" "查看基础配置模块文档"
    click D "./src/components/CLAUDE.md" "查看共享组件文档"
    click F "./src/store/CLAUDE.md" "查看状态管理文档"
```

## 模块索引

| 模块路径 | 功能职责 | 核心特性 | 状态 |
|---------|---------|---------|------|
| **src/views/schedule/** | 调度管理模块 | 来水预报、调度模型、仿真计算、方案评估 | ✅ 活跃开发 |
| **src/views/water-rain/** | 水雨情模块 | 实时监测、过程线分析、数据展示 | ✅ 活跃开发 |
| **src/views/early-warning/** | 预警系统模块 | 规则配置、信息发布、监测预警 | ✅ 活跃开发 |
| **src/views/project/** | 工程管理模块 | 水利工程信息、监测站点、对象管理 | ✅ 活跃开发 |
| **src/views/basic/** | 基础配置模块 | 站点管理、设备分类、权限配置 | ✅ 活跃开发 |
| **src/views/office/** | 办公应用模块 | 灌区资讯、供水管理、资源管理 | ✅ 活跃开发 |
| **src/views/archives/** | 档案管理模块 | 文件归档、云端存储、纸质档案 | 🔧 维护中 |
| **src/views/equipment/** | 设备管理模块 | 设备记录、运行状态、维护管理 | ✅ 活跃开发 |
| **src/views/video/** | 视频监控模块 | 实时监控、视频分屏、录像回放 | ✅ 活跃开发 |
| **src/views/briefing/** | 简报系统模块 | 简报生成、模板配置、计划管理 | ✅ 活跃开发 |
| **src/components/** | 共享组件库 | 通用组件、业务组件、工具组件 | ✅ 持续完善 |
| **src/store/** | 状态管理 | Vuex 模块、全局状态、权限控制 | ✅ 稳定运行 |

## 运行与开发

### 环境要求
```bash
# Node.js 版本要求
node >= 16.0.0
npm >= 7.0.0

# 推荐使用的包管理器
npm install --legacy-peer-deps
```

### 开发命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build:prod

# 构建测试版本
npm run build:test

# 代码检查
npm run lint
```

### 环境配置
- **开发环境**: `.env.development` - 本地开发调试
- **测试环境**: `.env.test` - 集成测试环境
- **生产环境**: `.env.production` - 正式生产环境

## 测试策略

### 当前测试覆盖
- **单元测试**: 暂未配置，建议后续补充 Jest + Vue Test Utils
- **集成测试**: 依赖后端接口联调测试
- **E2E 测试**: 暂未配置，建议使用 Cypress
- **代码检查**: ESLint + Prettier 代码规范检查

### 测试建议
1. 为核心业务组件添加单元测试
2. 对关键的调度算法和数据处理逻辑增加测试覆盖
3. 建立 E2E 测试套件验证关键业务流程

## 编码规范

### Git 提交规范
- `feat`: 新功能开发
- `fix`: 问题修复
- `refactor`: 代码重构
- `style`: 样式调整
- `docs`: 文档更新
- `test`: 测试相关
- `chore`: 构建配置等

### 代码规范
- 遵循 ESLint + Prettier 配置
- 组件命名使用 PascalCase
- 文件名使用 kebab-case
- 常量使用 UPPER_SNAKE_CASE

## AI 使用指引

### 项目特点
1. **水利行业专业性强**: 涉及大量专业术语和业务流程，如调度模型、来水预报、水位监测等
2. **GIS 地图集成**: 大量使用 MapBox 和地图可视化功能
3. **实时数据处理**: WebSocket 连接、实时监控数据展示
4. **复杂表格操作**: 使用 VXE Table 处理大量数据展示和编辑

### 开发建议
1. **熟悉业务域**: 了解水利工程、调度管理、监测预警等业务背景
2. **关注性能**: 地图渲染和大数据量表格需要性能优化
3. **移动端适配**: 考虑响应式设计和移动端访问需求
4. **数据安全**: 涉及重要基础设施数据，注意安全防护

### 常用开发模式
- **页面结构**: `index.vue` + `modules/` + `components/`
- **接口管理**: 统一在 `src/api/` 目录管理
- **状态管理**: 大型页面使用 Vuex，小型页面使用本地状态
- **样式组织**: 使用 Less 预处理器，支持主题定制