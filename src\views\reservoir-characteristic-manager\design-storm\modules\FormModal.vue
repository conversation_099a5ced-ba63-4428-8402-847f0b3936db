<template>
  <!-- 新增/编辑弹窗 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    modalHeight="600"
    @cancel="cancel"
  >
    <div slot="content">
      <div class="table-panel">
        <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          :label-col="{ span: 2 }"
          :wrapper-col="{ span: 10 }"
        >
          <!-- 阶段输入框 -->
          <a-form-model-item label="阶段" prop="stage">
            <a-input v-model="form.stage" placeholder="请输入阶段名称" />
          </a-form-model-item>

          <!-- 设计暴雨参数标题 -->
          <div class="section-title">设计暴雨参数</div>
          <div class="section-unit">单位：mm</div>

          <!-- 参数表格 -->
          <div class="parameter-table">
            <a-table
              :columns="parameterColumns"
              :data-source="parameterData"
              :pagination="false"
              bordered
              size="small"
            >
              <template slot="p333" slot-scope="text, record">
                <a-input-number
                  v-model="record.p333"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                />
              </template>
              <template slot="p2" slot-scope="text, record">
                <a-input-number
                  v-model="record.p2"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                />
              </template>
              <template slot="p02" slot-scope="text, record">
                <a-input-number
                  v-model="record.p02"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                />
              </template>
            </a-table>
          </div>

          <!-- 设计暴雨时程分配 -->
          <div class="section-title" style="margin-top: 20px;">设计暴雨时程分配</div>
          <a-form-model-item prop="fileUrl">
            <UploadFile
              :fileUrl.sync="form.fileUrl"
              :multiple="false"
              listType="text"
              folderName="design-storm"
              accept=".xlsx,.xls"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormModal',
    components: { AntModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        isEdit: false,
        
        // 表单参数
        form: {
          id: undefined,
          stage: '',
          fileUrl: '',
        },
        
        // 验证规则
        rules: {
          stage: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
        },

        // 参数表格列配置
        parameterColumns: [
          {
            title: '项目',
            dataIndex: 'item',
            width: 120,
            align: 'center',
          },
          {
            title: 'P=3.33%',
            dataIndex: 'p333',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'p333' },
          },
          {
            title: 'P=2%',
            dataIndex: 'p2',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'p2' },
          },
          {
            title: 'P=0.2%',
            dataIndex: 'p02',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'p02' },
          },
        ],

        // 参数表格数据
        parameterData: [
          { key: 'h1Point', item: 'H1点', p333: null, p2: null, p02: null },
          { key: 'h3Point', item: 'H3点', p333: null, p2: null, p02: null },
          { key: 'h6Point', item: 'H6点', p333: null, p2: null, p02: null },
          { key: 'h12Point', item: 'H12点', p333: null, p2: null, p02: null },
          { key: 'h24Point', item: 'H24点', p333: null, p2: null, p02: null },
          { key: 'h1Area', item: 'H1面', p333: null, p2: null, p02: null },
          { key: 'h3Area', item: 'H3面', p333: null, p2: null, p02: null },
          { key: 'h6Area', item: 'H6面', p333: null, p2: null, p02: null },
          { key: 'h12Area', item: 'H12面', p333: null, p2: null, p02: null },
          { key: 'h24Area', item: 'H24面', p333: null, p2: null, p02: null },
        ],
      }
    },
    methods: {
      // 新增
      add() {
        this.reset()
        this.formTitle = '新增设计暴雨'
        this.isEdit = false
        this.open = true
      },

      // 编辑
      edit(record) {
        this.reset()
        this.formTitle = '编辑设计暴雨'
        this.isEdit = true
        this.open = true
        
        // 填充表单数据
        this.form = {
          id: record.groupId,
          stage: record.stage,
          fileUrl: record.fileName || '',
        }
        
        // 填充参数表格数据（这里需要根据实际数据结构调整）
        // 由于测试数据中每个频率是单独一行，这里简化处理
        this.parameterData.forEach(item => {
          item.p333 = Math.random() * 50 + 20
          item.p2 = Math.random() * 80 + 30
          item.p02 = Math.random() * 120 + 50
        })
      },

      // 重置表单
      reset() {
        this.form = {
          id: undefined,
          stage: '',
          fileUrl: '',
        }
        
        // 重置参数表格
        this.parameterData.forEach(item => {
          item.p333 = null
          item.p2 = null
          item.p02 = null
        })
        
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
      },

      // 提交表单
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            
            // 模拟提交
            setTimeout(() => {
              this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
              this.loading = false
              this.cancel()
              this.$emit('ok')
            }, 1000)
          }
        })
      },

      // 取消
      cancel() {
        this.open = false
        this.reset()
        this.$emit('close')
      },
    },
  }
</script>

<style lang="less" scoped>
  .table-panel {
    padding: 10px 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 20px 0 10px 0;
    padding-left: 10px;
    border-left: 4px solid #1890ff;
  }
  .section-unit {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    padding-left: 10px;
  }

  .parameter-table {
    margin: 10px 0;
    
    /deep/ .ant-table-tbody > tr > td {
      padding: 8px;
    }
    
    /deep/ .ant-input-number {
      border-radius: 4px;
    }
  }
</style>
