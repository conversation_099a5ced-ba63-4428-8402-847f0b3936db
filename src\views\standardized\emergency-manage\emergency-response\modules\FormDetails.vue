<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案名称：</label>
            <span class="common-value-text">{{ form.planName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案类型：</label>
            <span class="common-value-text">{{ form.planType }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">事件编码：</label>
            <span class="common-value-text">{{ form.eventCode }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">启动时间：</label>
            <span class="common-value-text">{{ form.startTime }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">{{ form.projectName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">负责人：</label>
            <span class="common-value-text">{{ form.chargeName }}</span>
          </div>
        </a-col>

        <!-- 响应等级区块 -->
        <a-col :lg="24" :md="24" :sm="24" class="response-level-block">
          <div class="response-level-container">
            <h3 class="response-level-title">{{ form.responseLevel }}级响应</h3>

            <div class="response-content">
              <label class="response-label">响应内容：</label>
              <div class="response-content-text">{{ form.responseContent }}</div>
            </div>

            <div class="response-condition">
              <label class="response-label">响应条件：</label>
              <div class="response-content-text">{{ form.responseCondition }}</div>
            </div>

            <div class="response-config">
              <div class="alarm-content-section">
                <label class="response-label">报警内容：</label>
                <div class="response-content-text">{{ form.alarmContent }}</div>
              </div>

              <div class="receivers-section">
                <label class="response-label">接收人：</label>
                <div class="receivers-tags">
                  <a-tag
                    v-for="(receiver, index) in form.receivers"
                    :key="index"
                    color="blue"
                    class="receiver-tag"
                  >
                    {{ receiver }}
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'

export default {
  name: 'FormDetails',
  components: { AntModal },
  data() {
    return {
      modalLoading: false,
      formTitle: '详情',
      form: {
        planName: '',
        planType: '',
        eventCode: '',
        startTime: '',
        projectName: '',
        chargeName: '',
        responseLevel: '',
        responseContent: '',
        responseCondition: '',
        alarmContent: '',
        receivers: []
      },
      open: false,
      // 测试数据
      testData: [
        {
          id: 1,
          planName: '防洪应急预案',
          planType: '防洪预案',
          eventCode: 'ER2024001',
          startTime: '2024-01-15 08:30:00',
          projectName: '某某水库工程',
          chargeName: '张三',
          responseLevel: 'I',
          responseContent: 'I级响应：启动最高级别应急预案，全面动员相关部门和人员，实施紧急抢险救援措施，确保人员安全和工程安全。',
          responseCondition: '水位超过警戒线10cm，降雨量达到50mm/h',
          alarmContent: '1. 水位超过警戒线10cm，立即启动I级响应\n2. 降雨量达到50mm/h，加强监测预警\n3. 下游出现险情，紧急疏散人员\n4. 工程设施出现异常，立即抢修',
          receivers: ['调度中心主任', '值班工程师', '应急指挥部', '防汛办主任', '水库管理员']
        },
        {
          id: 2,
          planName: '抗旱应急预案',
          planType: '抗旱预案',
          eventCode: 'ER2024002',
          startTime: '2024-01-20 14:20:00',
          projectName: '某某灌区工程',
          chargeName: '李四',
          responseLevel: 'II',
          responseContent: 'II级响应：启动重大级别应急预案，相关部门协调响应，加强水源调度和用水管理。',
          responseCondition: '水位超过警戒线5cm',
          alarmContent: '1. 水位超过警戒线5cm，启动II级响应\n2. 供水压力不足，调整供水计划\n3. 用水需求增加，优化水资源配置',
          receivers: ['调度员', '运行值班员', '灌区管理员']
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.$emit('close')
    },

    /** 查看详情 */
    details(row) {
      this.open = true
      this.formTitle = '详情'
      this.modalLoading = true

      // 模拟API调用，使用测试数据
      setTimeout(() => {
        const testItem = this.testData.find(item => item.id === row.id)
        if (testItem) {
          this.form = { ...testItem }
        }
        this.modalLoading = false
      }, 300)
    },
  },
}
</script>

<style lang="less" scoped>
@import url('~@/global.less');

.item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.response-level-block {
  margin-bottom: 24px;

  .response-level-container {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;
  }

  .response-level-title {
    font-size: 16px;
    font-weight: 600;
    color: #111;
    margin-bottom: 16px;
    margin-top: 0;
  }

  .response-content, .response-condition {
    margin-bottom: 16px;

    .response-label {
      display: inline-block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .response-content-text {
      background-color: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px 12px;
      min-height: 40px;
      line-height: 1.5;
      color: #262626;
    }
  }

  .response-config {
    .alarm-content-section, .receivers-section {
      margin-bottom: 16px;

      .response-label {
        display: inline-block;
        margin-bottom: 8px;
        font-weight: 500;
      }
    }

    .receivers-section {
      .receivers-tags {
        .receiver-tag {
          margin-bottom: 8px;
          margin-right: 8px;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>