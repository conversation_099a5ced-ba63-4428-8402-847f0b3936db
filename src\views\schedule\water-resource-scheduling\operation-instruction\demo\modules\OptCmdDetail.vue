<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="720"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作票编号：</label>
            <span class="common-value-text">
              {{ objDetail?.operateCode }}
            </span>
          </div>
        </a-col>
        <!-- <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作任务：</label>
            <span class="common-value-text">
              {{ objDetail?.operateTask }}
            </span>
          </div>
        </a-col> -->

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作日期：</label>
            <span class="common-value-text">
              {{ objDetail?.operateDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作人：</label>
            <span class="common-value-text">
              {{ objDetail?.operateName }}
            </span>
          </div>
        </a-col>

        <!-- <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">发令人：</label>
            <span class="common-value-text">
              {{ objDetail?.starterName }}
            </span>
          </div>
        </a-col> -->

        <!-- <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">发令时间：</label>
            <span class="common-value-text">
              {{ objDetail?.starterDate }}
            </span>
          </div>
        </a-col> -->

        <!-- <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">受令人：</label>
            <span class="common-value-text">
              {{ objDetail?.acceptorName }}
            </span>
          </div>
        </a-col> -->

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">监护人：</label>
            <span class="common-value-text">
              {{ objDetail?.guardianName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作开始时间：</label>
            <span class="common-value-text">
              {{ objDetail?.startDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作结果时间：</label>
            <span class="common-value-text">
              {{ objDetail?.endDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">备注：</label>
            <span class="common-value-text">
              {{ objDetail?.remark }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">操作项目</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <VxeTable
              ref="vxeTableRef"
              height="260"
              :isShowTableHeader="false"
              :columns="columns"
              :tableData="tableList"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">关闭</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import moment from 'moment'
  import { getOperateCmdById } from '../services'

  export default {
    name: 'OptCmdDetail',
    components: { AntModal, VxeTable },
    props: [],
    data() {
      return {
        formTitle: '操作票详情',
        modalLoading: false,
        open: false,
        systemUserOptions: [],
        objDetail: {},
        tableList: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '内容',
            field: 'content',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
          {
            title: '结果',
            field: 'status',
            width: 60,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.status == 1 ? (
                  <a-icon type='check' style='color:green' />
                ) : (
                  <a-icon type='close' style='color:red' />
                )
              },
            },
          },
        ],
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 按钮操作 */
      handle(row) {
        this.open = true
        getOperateCmdById({
          operateCmdId: row.operateCmdId,
        }).then(res => {
          if (res.code == 200) {
            this.objDetail = res?.data
            this.tableList = this.objDetail.operateCmdDetailsList
          }
          this.modalLoading = false
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
