<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工作票编码" prop="cmdCode">
              <a-input v-model="form.cmdCode" placeholder="请输入" allow-clear @input="onCmdCodeInput" />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工作票名称" prop="cmdName">
              <a-input v-model="form.cmdName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度方案" prop="dispatchId">
              <a-select
                show-search
                allow-clear
                v-model="form.dispatchId"
                :options="dispatchOptions"
                placeholder="请选择"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度类型" prop="dispatchTypeCode">
              <a-select
                v-model="form.dispatchTypeCode"
                placeholder="请选择"
                allow-clear
                :options="dispatchTypeOptions"
              >
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="计划工作时间" prop="planTime">
              <a-range-picker
                allow-clear
                showTime
                format="YYYY-MM-DD HH:mm"
                valueFormat="YYYY-MM-DD HH:mm"
                :placeholder="['开始时间', '结束时间']"
                v-model="form.planTime"
                :disabledDate="disabledDate"
                @change="onRangeChange"
              />
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="注意事项(安全措施)" prop="items">
              <a-textarea v-model="form.items" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24">
            <div class="title">工程信息</div>
            <div class="project-table-container">
              <div class="table-header">
                <a-button type="primary" @click="addProjectRow" style="margin-bottom: 16px;">
                  <a-icon type="plus" />
                  新增工程
                </a-button>
              </div>
              <a-table
                :columns="projectColumns"
                :data-source="form.projectList"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: 600 }"
              >
                <template slot="projectName" slot-scope="text, record, index">
                  <a-select
                    v-model="record.projectId"
                    :options="projectOptions"
                    placeholder="请选择工程"
                    style="width: 100%"
                    @change="(value) => handleProjectSelectChange(value, index)"
                  />
                </template>
                <template slot="projectManager" slot-scope="text, record, index">
                  <a-select
                    v-model="record.wardUserId"
                    :options="record.userOptions || []"
                    placeholder="请选择负责人"
                    style="width: 100%"
                  />
                </template>
                <template slot="remark" slot-scope="text, record, index">
                  <a-input
                    v-model="record.remark"
                    placeholder="请输入备注"
                  />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a-button
                    type="link"
                    danger
                    @click="removeProjectRow(index)"
                  >
                    删除
                  </a-button>
                </template>
              </a-table>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  // import { addRunmd, editRunmd, getRunmdById, getDeviceByProjectId, isRunmdCodeExist } from '../services'
  // import { getDispatchProjectList, getUserList } from '../../dispatch-project/services'
  // import { getDispatchPage } from '../../dispatch-case/services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: ['dispatchOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        projectOptions: [],
        dispatchTypeOptions: [],
        formTitle: '',
        projectColumns: [
          {
            title: '工程名称',
            dataIndex: 'projectName',
            key: 'projectName',
            width: 200,
            scopedSlots: { customRender: 'projectName' },
          },
          {
            title: '工程负责人',
            dataIndex: 'projectManager',
            key: 'projectManager',
            width: 150,
            scopedSlots: { customRender: 'projectManager' },
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            width: 200,
            scopedSlots: { customRender: 'remark' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 80,
            scopedSlots: { customRender: 'action' },
          },
        ],
        form: {
          cmdCode: '',
          cmdName: '',
          planEndDate: null,
          planStartDate: null,
          items: '',
          dispatchTypeCode: undefined,
          projectList: [],
        },
        open: false,
        rules: {
          cmdCode: [
            { required: true, message: '工作票编码不能为空', trigger: 'blur' },
            { validator: this.validateCmdCode, trigger: 'blur' }
          ],
          cmdName: [{ required: true, message: '工作票名称不能为空', trigger: 'blur' }],
          dispatchId: [{ required: true, message: '调度方案不能为空', trigger: 'change' }],
          planTime: [{ required: true, message: '计划工作时间不能为空', trigger: 'change' }],
          dispatchTypeCode: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
        },
        debounceTimer: null,
      }
    },
    // created() {
    //   this.getDispatchTypeOptions()
    // },
    // mounted() {},
    // methods: {
    //   // 禁用今天之前的日期
    //   disabledDate(current) {
    //     // 禁用今天之前的日期
    //     return current && current < moment().startOf('day');
    //   },
    //   // 获取调度类型下拉选项
    //   getDispatchTypeOptions() {
    //     getOptions('runCmdDispatchType').then(res => {
    //       if (res.code == 200) {
    //         this.dispatchTypeOptions = res.data.map(item => ({
    //           label: item.value,
    //           value: item.key
    //         }))
    //       }
    //     })
    //   },
    //   onRangeChange(dates) {
    //     this.form.planStartDate = dates[0]
    //     this.form.planEndDate = dates[1]
    //   },
    //   // 取消按钮
    //   cancel() {
    //     this.open = false
    //     this.$emit('close')
    //   },
    //   // 新增工程行
    //   addProjectRow() {
    //     this.form.projectList.push({
    //       projectId: null,
    //       wardUserId: null,
    //       remark: '',
    //       userOptions: [],
    //     })
    //   },
    //   // 删除工程行
    //   removeProjectRow(index) {
    //     this.form.projectList.splice(index, 1)
    //   },
    //   // 处理工程选择变化
    //   handleProjectSelectChange(value, index) {
    //     const project = this.projectOptions.find(item => item.value === value)
    //     if (project) {
    //       // 加载该工程的用户列表
    //       this.loadUserOptions(project.dispatchProjectId).then(userOptions => {
    //         this.$set(this.form.projectList[index], 'userOptions', userOptions)
    //         this.$set(this.form.projectList[index], 'wardUserId', null)
    //       })
    //     }
    //   },
    //   /** 新增按钮操作 */
    //   handle(row) {
    //     this.open = true
    //     this.formTitle = row.action
    //     getDispatchProjectList().then(res => {
    //       if (res.code == 200) {
    //         this.projectOptions = res?.data?.map(el => ({
    //           label: el.projectName,
    //           value: el.projectId,
    //           dispatchProjectId: el.dispatchProjectId,
    //         }))
    //       }
    //     })

    //     if (row.action == '修改' || row.action == '复制') {
    //       this.modalLoading = true

    //       getRunmdById({ runCmdId: row.runCmdId }).then(res => {
    //         if (res.code == 200) {
    //           this.form = {
    //             ...res.data,
    //             projectList: res.data.projectList || [],
    //           }

    //           if (row.action == '复制') {
    //             this.form.runCmdId = null
    //           }

    //           // 为已有的工程项加载用户选项
    //           this.form.projectList.forEach((project, index) => {
    //             if (project.projectId) {
    //               const projectOption = this.projectOptions.find(item => item.value === project.projectId)
    //               if (projectOption) {
    //                 this.loadUserOptions(projectOption.dispatchProjectId).then(userOptions => {
    //                   this.$set(this.form.projectList[index], 'userOptions', userOptions)
    //                 })
    //               }
    //             }
    //           })

    //           this.$set(this.form, 'planTime', [this.form.planStartDate, this.form.planEndDate])
    //           this.modalLoading = false
    //         }
    //       })
    //     } else {
    //       // 新增时初始化一个空的工程行
    //       this.form.projectList = []
    //     }
    //   },

    //   loadUserOptions(dispatchProjectId) {
    //     return new Promise((resolve, reject) => {
    //       let options = []
    //       if (dispatchProjectId) {
    //         getUserList({ dispatchProjectId }).then(res => {
    //           if (res.code == 200) {
    //             options = res?.data?.map(el => ({
    //               label: el.name,
    //               value: el.userId,
    //             }))
    //           }
    //           resolve(options)
    //         }).catch(() => {
    //           resolve(options)
    //         })
    //       } else {
    //         resolve(options)
    //       }
    //     })
    //   },

    //   /** 提交按钮 */
    //   submitForm() {
    //     this.$refs.form.validate(valid => {
    //       if (valid) {
    //         this.loading = true
    //         this.form.planStartDate = moment(this.form.planStartDate).format('YYYY-MM-DD HH:mm:ss')
    //         this.form.planEndDate = moment(this.form.planEndDate).format('YYYY-MM-DD HH:mm:ss')
    //         this.form.wardUserIds = this.form.projectList.length > 0 
    //           ? this.form.projectList[0].wardUserId
    //           : '1'
    //         this.form.workUserIds = this.form.projectList.length > 0 
    //           ? this.form.projectList.map(item => item.wardUserId)
    //           : []
    //         this.form.projectId = this.form.projectList.length > 0 
    //           ? this.form.projectList[0].projectId
    //           : '1'
    //         this.form.deviceOpenTimes = [
    //           {
    //             deviceId: '1',
    //             startDate: this.form.planStartDate,
    //             endDate: this.form.planEndDate,
    //           }
    //         ]
    //         if (this.form.runCmdId == null) {
    //           addRunmd(this.form)
    //             .then(res => {
    //               if (res.code == 200) {
    //                 this.$message.success('新增成功', 3)
    //                 this.open = false
    //                 this.$emit('ok')
    //               }
    //             })
    //             .catch(() => (this.loading = false))
    //         } else {
    //           editRunmd(this.form)
    //             .then(res => {
    //               if (res.code == 200) {
    //                 this.$message.success('修改成功', 3)
    //                 this.open = false
    //                 this.$emit('ok')
    //               }
    //             })
    //             .catch(() => (this.loading = false))
    //         }
    //       }
    //     })
    //   },
    //   // 工作票编码输入防抖校验
    //   onCmdCodeInput() {
    //     if (this.debounceTimer) clearTimeout(this.debounceTimer)
    //     this.debounceTimer = setTimeout(() => {
    //       if (!this.form.cmdCode) return
    //       this.$refs.form.validateField('cmdCode')
    //     }, 2000)
    //   },
    //   // 工作票编码唯一性校验
    //   validateCmdCode(rule, value, callback) {
    //     // 只在新增时校验，修改时不校验自己
    //     if (!value) return callback()
    //     if (this.form.runCmdId) return callback()
    //     isRunmdCodeExist({ cmdCode: value }).then(res => {
    //       if (res.code === 200 && res.data === true) {
    //         callback(new Error('该工作票编码已存在'))
    //       } else {
    //         callback()
    //       }
    //     }).catch(() => {
    //       callback()
    //     })
    //   },
    // },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .project-table-container {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;

    .table-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }

  ::v-deep .ant-table-tbody > tr > td {
    padding: 8px;
  }

  ::v-deep .ant-table-thead > tr > th {
    padding: 8px;
    background-color: #f5f5f5;
  }
</style>

