<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="720"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">调度令编码：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdCode }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度令名称：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度方案：</label>
            <span class="common-value-text">
              {{ objDetail?.dispatchCode }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">计划工作时间：</label>
            <span class="common-value-text">
              {{ objDetail?.planWorkTime }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">工作负责人：</label>
            <span class="common-value-text">
              {{ objDetail?.workManager }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">当前状态：</label>
            <span class="common-value-text" :style="{ color: getStatusColor(objDetail?.status) }">
              {{ getStatusText(objDetail?.status) }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">工程信息</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px;height: 200px">
            <VxeTable
              ref="vxeProjectTableRef"
              :height="200"
              :isShowTableHeader="false"     
              :columns="projectColumns"
              :tableData="objDetail?.projectList || []"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>

        <template v-if="mode === 'view' && (objDetail?.status === 2 || objDetail?.status === 3)">
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px">
              <div class="title">审批信息</div>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px;height: 150px">
              <VxeTable
                ref="vxeAuditTableRef"
                :height="150"
                :isShowTableHeader="false"     
                :columns="auditColumns"
                :tableData="objDetail?.auditList || []"
                :rowConfig="{ isCurrent: true, isHover: true }"
              ></VxeTable>
            </div>
          </a-col>
        </template>

        <template v-if="mode === 'view' && objDetail?.status === 3">
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px">
              <div class="title">工程操作信息</div>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px;height: 200px">
              <VxeTable
                ref="vxeProjectOpInfoTableRef"
                :height="200"
                :isShowTableHeader="false"     
                :columns="projectOpInfoColumns"
                :tableData="objDetail?.projectOpInfoList || []"
                :rowConfig="{ isCurrent: true, isHover: true }"
              ></VxeTable>
            </div>
          </a-col>
        </template>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">{{ mode === 'receive' ? '取消' : '关闭' }}</a-button>
      <a-button v-if="mode === 'receive'" type="primary" @click="handleReceive" :loading="submitLoading">接收</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import moment from 'moment'

  export default {
    name: 'DetailModal',
    components: { AntModal, VxeTable },
    props: [],
    data() {
      return {
        formTitle: '调度指令详情',
        modalLoading: false,
        open: false,
        mode: 'view', // view, receive
        objDetail: {},
        submitLoading: false,
        projectColumns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '负责人',
            field: 'manager',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '开闸时间',
            field: 'openTime',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '关闸时间',
            field: 'closeTime',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
        ],
        auditColumns: [
          {
            title: '审批人',
            field: 'auditUser',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '审批时间',
            field: 'auditTime',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
          {
            title: '审批意见',
            field: 'auditResult',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '备注信息',
            field: 'auditRemark',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
        ],
        projectOpInfoColumns: [
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作时间',
            field: 'operateDate',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '操作人',
            field: 'operateName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '监护人',
            field: 'guardianName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
        ],
      }
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      getStatusText(status) {
        const statusMap = {
          0: '审批中',
          1: '下发中',
          2: '已驳回',
          3: '已完成'
        }
        return statusMap[status] || '未知'
      },

      getStatusColor(status) {
        const colorMap = {
          0: '#1890ff',
          1: '#52c41a',
          2: '#ff4d4f',
          3: '#8c8c8c'
        }
        return colorMap[status] || '#8c8c8c'
      },

      /** 按钮操作 */
      handle(row, mode = 'view') {
        this.open = true
        this.mode = mode
        this.modalLoading = true
        
        // 设置标题
        const titleMap = {
          view: '查看调度指令',
          receive: '接收调度指令',
        }
        this.formTitle = titleMap[mode] || '调度指令详情'
        
        // 模拟获取详情数据
        setTimeout(() => {
          this.objDetail = {
            ...row,
            auditList: row.status >= 2 ? [{
              auditResult: row.status === 2 ? '驳回' : '同意',
              auditTime: '2024-03-11 14:30:00',
              auditUser: '审核员',
              auditRemark: row.status === 2 ? '需要重新确认工程信息' : '审核通过',
            }] : [],
            projectOpInfoList: row.status === 3 ? [{
              projectName: '水库闸门工程',
              operateDate: '2024-03-15 08:30:00',
              operateName: '操作员A',
              guardianName: '监护员B',
            }] : []
          }
          this.modalLoading = false
        }, 500)
      },

      handleReceive() {
        this.submitLoading = true
        
        // 模拟接收操作
        setTimeout(() => {
          this.$message.success('接收成功')
          // 通知父组件刷新列表
          this.$emit('receiveResult', this.objDetail)
          this.cancel()
          this.submitLoading = false
        }, 1000)
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 20px;
  }
</style>
