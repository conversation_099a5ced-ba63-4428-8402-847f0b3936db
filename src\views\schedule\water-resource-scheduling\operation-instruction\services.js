import request from '@/utils/request'

// 模拟测试数据
const mockData = [
  {
    id: 1,
    operateCode: 'CZ202401001',
    projectName: '水库闸门工程',
    operateContent: '开启1号闸门至50%开度',
    cmdCode: 'DD202401001',
    cmdName: '春季防汛调度指令',
    operateName: '张三',
    guardianName: '李四',
    operateTime: '2024-03-15 08:30 - 2024-03-15 09:00',
    startDate: '2024-03-15 08:30:00',
    endDate: '2024-03-15 09:00:00',
    operateDate: '2024-03-15',
    remark: '操作顺利完成，闸门开度达到预期',
    // 关联的调度指令详情
    dispatchDetail: {
      id: 1,
      cmdCode: 'DD202401001',
      cmdName: '春季防汛调度指令',
      dispatchCode: '防汛调度方案A',
      planWorkTime: '2024-03-15 08:00 ~ 2024-03-15 18:00',
      workManager: '张三',
      status: 3,
      projectList: [
        {
          projectName: '水库闸门工程',
          manager: '李四',
          openTime: '2024-03-15 08:00',
          closeTime: '2024-03-15 18:00',
          remark: '注意安全操作，确保闸门开启到位'
        },
        {
          projectName: '排水泵站工程',
          manager: '王五',
          openTime: '2024-03-15 09:00',
          closeTime: '2024-03-15 17:00',
          remark: '监控水位变化，及时调整泵站运行'
        }
      ]
    },
    // 操作项目详情
    operateDetails: [
      { content: '检查闸门状态', status: 1 },
      { content: '启动闸门控制系统', status: 1 },
      { content: '开启闸门至50%开度', status: 1 },
      { content: '确认水位变化', status: 1 },
      { content: '记录操作数据', status: 1 }
    ]
  },
  {
    id: 2,
    operateCode: 'CZ202401002',
    projectName: '灌溉渠道工程',
    operateContent: '开启灌溉渠道进水闸',
    cmdCode: 'DD202401002',
    cmdName: '夏季灌溉调度指令',
    operateName: '王五',
    guardianName: '赵六',
    operateTime: '2024-06-20 06:15 - 2024-06-20 06:45',
    startDate: '2024-06-20 06:15:00',
    endDate: '2024-06-20 06:45:00',
    operateDate: '2024-06-20',
    remark: '灌溉渠道开启正常，水流稳定',
    // 关联的调度指令详情
    dispatchDetail: {
      id: 2,
      cmdCode: 'DD202401002',
      cmdName: '夏季灌溉调度指令',
      dispatchCode: '灌溉调度方案B',
      planWorkTime: '2024-06-20 06:00 ~ 2024-06-20 20:00',
      workManager: '王五',
      status: 3,
      projectList: [
        {
          projectName: '灌溉渠道工程',
          manager: '赵六',
          openTime: '2024-06-20 06:00',
          closeTime: '2024-06-20 20:00',
          remark: '确保水量充足，按时开启灌溉渠道'
        },
        {
          projectName: '提水泵站工程',
          manager: '钱七',
          openTime: '2024-06-20 06:30',
          closeTime: '2024-06-20 19:30',
          remark: '配合渠道工程，保证灌溉用水需求'
        }
      ]
    },
    // 操作项目详情
    operateDetails: [
      { content: '检查渠道水位', status: 1 },
      { content: '清理渠道杂物', status: 1 },
      { content: '开启进水闸门', status: 1 },
      { content: '调节水流量', status: 1 },
      { content: '监测下游水位', status: 1 }
    ]
  },
  {
    id: 3,
    operateCode: 'CZ202401003',
    projectName: '排水泵站工程',
    operateContent: '启动2号排水泵',
    cmdCode: 'DD202401001',
    cmdName: '春季防汛调度指令',
    operateName: '李四',
    guardianName: '张三',
    operateTime: '2024-03-15 10:00 - 2024-03-15 10:30',
    startDate: '2024-03-15 10:00:00',
    endDate: '2024-03-15 10:30:00',
    operateDate: '2024-03-15',
    remark: '排水泵启动正常，排水效果良好',
    // 关联的调度指令详情
    dispatchDetail: {
      id: 1,
      cmdCode: 'DD202401001',
      cmdName: '春季防汛调度指令',
      dispatchCode: '防汛调度方案A',
      planWorkTime: '2024-03-15 08:00 ~ 2024-03-15 18:00',
      workManager: '张三',
      status: 3,
      projectList: [
        {
          projectName: '水库闸门工程',
          manager: '李四',
          openTime: '2024-03-15 08:00',
          closeTime: '2024-03-15 18:00',
          remark: '注意安全操作，确保闸门开启到位'
        },
        {
          projectName: '排水泵站工程',
          manager: '王五',
          openTime: '2024-03-15 09:00',
          closeTime: '2024-03-15 17:00',
          remark: '监控水位变化，及时调整泵站运行'
        }
      ]
    },
    // 操作项目详情
    operateDetails: [
      { content: '检查泵站设备状态', status: 1 },
      { content: '启动控制系统', status: 1 },
      { content: '开启2号排水泵', status: 1 },
      { content: '监测排水流量', status: 1 },
      { content: '确认排水效果', status: 1 },
      { content: '记录运行参数', status: 1 }
    ]
  }
]

// 分页查询操作指令
export function getOperateCmdPage(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟分页查询
      const { pageNum = 1, pageSize = 10, operateCode, cmdCode, startDate, endDate } = data
      
      let filteredData = [...mockData]
      
      // 筛选条件
      if (operateCode) {
        filteredData = filteredData.filter(item => item.operateCode.includes(operateCode))
      }
      if (cmdCode) {
        filteredData = filteredData.filter(item => item.cmdCode.includes(cmdCode))
      }
      if (startDate && endDate) {
        filteredData = filteredData.filter(item => {
          const itemDate = new Date(item.operateDate)
          const start = new Date(startDate)
          const end = new Date(endDate)
          return itemDate >= start && itemDate <= end
        })
      }
      
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)
      
      resolve({
        code: 200,
        data: {
          data: list,
          total: filteredData.length,
          pageNum,
          pageSize
        }
      })
    }, 500)
  })
}

// 根据ID获取操作指令详情
export function getOperateCmdById(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.id === params.operateCmdId)
      resolve({
        code: 200,
        data: item || {}
      })
    }, 300)
  })
}

// 同步操作指令（模拟功能）
export function syncOperateCmd() {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟同步操作，返回同步的记录数
      const syncCount = Math.floor(Math.random() * 5) + 1
      resolve({
        code: 200,
        data: syncCount
      })
    }, 1000)
  })
}

// 导出模拟数据供其他地方使用
export { mockData }
