[根目录](../../CLAUDE.md) > [src](../) > **store**

# 状态管理架构文档

## 变更记录 (Changelog)

### 2025-09-01 13:58:17
- **[初始化]** 完成 Vuex 状态管理架构分析
- **[识别]** 发现 app、user、permission、size 四个核心模块

## 模块职责

基于 Vuex 3.6.2 的全局状态管理，负责应用程序的状态集中管理和数据流控制：
- **应用状态管理**: 侧边栏、设备信息、全局配置等应用级状态
- **用户状态管理**: 用户信息、权限数据、登录状态维护
- **权限控制**: 动态路由生成、菜单权限、按钮级权限控制
- **界面状态**: 组件尺寸、主题配置、布局状态等

## 入口与启动

### Store 初始化
```javascript
// src/store/index.js
import Vue from 'vue'
import Vuex from 'vuex'

export default new Vuex.Store({
  modules: {
    app,           // 应用状态
    user,          // 用户状态  
    size,          // 界面尺寸
    permission,    // 权限控制
  },
  getters        // 全局计算属性
})
```

### 模块注册
所有模块遵循标准的 Vuex 模块结构：
```javascript
export default {
  namespaced: true,
  state: {},
  mutations: {},
  actions: {},
  getters: {}
}
```

## 对外接口

### 状态访问 API
```javascript
// 在组件中访问状态
computed: {
  ...mapState('user', ['userInfo', 'permissions']),
  ...mapGetters(['sidebar', 'avatar', 'name'])
}
```

### 状态更新 API
```javascript
// 触发状态变更
methods: {
  ...mapActions('user', ['login', 'logout', 'getUserInfo']),
  ...mapMutations('app', ['TOGGLE_SIDEBAR'])
}
```

## 关键依赖与配置

### 核心依赖
```json
{
  "vuex": "^3.6.2",           // 状态管理核心
  "vue-router": "^3.6.5",    // 路由管理(权限模块)
  "js-cookie": "^3.0.1"      // Cookie操作
}
```

### 持久化配置
- 用户登录状态通过 Cookie 持久化
- 应用配置通过 localStorage 存储
- 敏感信息采用内存存储，页面刷新后重新获取

## 数据模型

### 用户状态模型
```javascript
// user 模块状态结构
{
  userInfo: {
    id: String,
    username: String,
    realname: String,
    avatar: String,
    roles: Array,
    permissions: Array
  },
  token: String,
  welcome: String,
  permissions: Array,
  roles: Array
}
```

### 应用状态模型
```javascript
// app 模块状态结构  
{
  sidebar: {
    opened: Boolean,
    withoutAnimation: Boolean
  },
  device: 'desktop|mobile',
  theme: String,
  layout: String,
  contentWidth: String,
  multiTab: Boolean
}
```

### 权限状态模型
```javascript
// permission 模块状态结构
{
  routers: Array,           // 完整路由配置
  addRouters: Array,        // 动态添加的路由
  permissionList: Array     // 权限列表
}
```

## 测试与质量

### 测试策略
- **单元测试**: 对 mutations 和 actions 进行单元测试
- **集成测试**: 验证模块间的数据流和交互
- **状态快照**: 关键状态变更的快照测试

### 质量保证
- 严格的状态变更追踪
- 开发环境下的 Vue DevTools 集成
- 状态变更日志记录

## 常见问题 (FAQ)

### Q: 如何处理异步数据加载？
A: 通过 actions 处理异步操作，使用 async/await 或 Promise 确保数据正确加载。

### Q: 权限变更后如何刷新路由？
A: 调用 `permission/GenerateRoutes` action 重新生成路由，并使用 `router.addRoutes` 动态添加。

### Q: 如何避免状态管理过于复杂？
A: 遵循单一职责原则，合理划分模块，避免在 Vuex 中存储本地状态。

## 相关文件清单

### 核心文件
```
src/store/
├── index.js                  # Store 入口文件
├── getters.js               # 全局计算属性
└── modules/                 # 状态模块目录
    ├── app.js              # 应用状态模块
    ├── user.js             # 用户状态模块
    ├── size.js             # 界面尺寸模块
    └── async-router.js     # 权限路由模块
```

### 相关工具
```
src/utils/
├── request.js              # HTTP 请求拦截器
└── auth.js                 # 认证工具函数
```

### 权限控制
```
src/
├── permission.js           # 路由权限守卫
└── directive/
    └── permission.js       # 权限指令
```