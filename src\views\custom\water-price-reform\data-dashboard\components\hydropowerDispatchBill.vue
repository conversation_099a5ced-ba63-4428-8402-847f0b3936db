<template>
  <base-echart id="bar-line-echart" class="bar-line-echart" :width="width" :height="height" :option="options" />
</template>

<script lang="jsx">
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'
  const colors = ['#3491FA', '#0FC6C2']
  export default {
    components: {
      BaseEchart,
    },
    props: {
      title: { default: '用水户' },
      dispatchStatistics: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      siteTerminalData: { default: () => [] },
      width: { default: '100%' },
      height: { default: '500px' },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dispatchStatistics)
      },
    },
    methods: {
      getOptions(dispatchResult) {
        const newList = dispatchResult?.map(item => item.value * 360)
        const newList2 = dispatchResult?.map(item => item.value)
        let tmpRight = Math.max(...newList)
        let tmpLeft = Math.max(...newList2)
        const maxLeftValue = (tmpLeft * 1.2)?.toFixed(0)
        const maxRightValue = (tmpRight * 1.2)?.toFixed(0)
        const legendArr = ['用水量', '用水量费用'] // ['临时调度', this.title]
        const option = {
          grid: {
            top: 60,
            bottom: 30,
            left: 60,
            right: 60,
          },
          legend: {
            left: 'center',
            data: legendArr,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // type: 'shadow',
              type: 'cross',
              crossStyle: {
                color: '#999',
              },
            },
          },
          xAxis: {
            axisTick: { show: true },
            axisLine: { lineStyle: { color: 'rgba(115, 115, 115, 1)' } },
            axisLabel: {
              textStyle: { fontSize: 12, color: '#777' },
              lineStyle: {
                color: '#90979c',
              },
            },
            data: dispatchResult.map(el => el.dateTime),
          },
          yAxis: [
            {
              type: 'value',
              name: '用水量(万m³)',
              axisLabel: {
                fontSize: 12,
                formatter: '{value}',
              },
              position: 'left',
              min: 0,
              max: maxLeftValue, // 根据实际情况调整
              //scale: true,
              splitLine: {
                show: false, // 隐藏左侧Y轴分割线
              },
            },
            {
              type: 'value',
              name: '用水量费用(元)',
              axisLabel: {
                fontSize: 12,
                formatter: '{value}',
              },
              // scale: true,
              position: 'right',
              min: 0,
              max: maxRightValue, // 根据实际情况调整
              //interval: null
            },
          ],
          series: [
            {
              name: legendArr[0],
              color: colors[0],
              type: 'bar',
              // stack: '用水量',
              barMaxWidth: 10,
              data: dispatchResult.map(el => el.value),
            },
            {
              name: legendArr[1],
              color: colors[1],
              type: 'line',
              yAxisIndex: 1,
              // stack: '用水量费用',
              lineStyle: {
                width: 3,
              },
              symbol: 'circle',
              symbolSize: 4,
              data: dispatchResult.map(el => (el.value * 360)?.toFixed(2)),
            },
          ],
        }

        return option
      },
    },
  }
</script>
<style lang="less" scoped></style>
