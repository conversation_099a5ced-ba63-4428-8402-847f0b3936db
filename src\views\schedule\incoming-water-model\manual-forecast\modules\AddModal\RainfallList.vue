<template>
  <div class="rainfall-container">
    <!-- 左侧：降雨统计 -->
    <div class="left-panel">
      <div class="panel-header">
        <h3>降雨统计</h3>
      </div>
      
      <!-- 缩放系数 -->
      <div class="scale-section">
        <label>缩放系数：</label>
        <a-slider
          v-model="scaleValue"
          :min="0.1"
          :max="5"
          :step="0.1"
          style="width: 200px; margin: 0 10px;"
          @change="handleScaleChange"
        />
        <a-input-number
          v-model="scaleValue"
          :min="0.1"
          :max="5"
          :step="0.1"
          :precision="1"
          size="small"
          style="width: 80px;"
          @change="handleScaleChange"
        />
      </div>

      <!-- 柱状图 -->
      <div class="chart-section">
        <div ref="chartContainer" style="width: 100%; height: 500px; position: relative;"></div>
        <!-- 悬浮输入框 -->
        <div
          v-if="hoverTooltip.show"
          :style="{
            position: 'absolute',
            left: hoverTooltip.x + 'px',
            top: hoverTooltip.y + 'px',
            background: 'linear-gradient(135deg, rgba(253, 254, 255, 0.6) 0%, rgba(244, 247, 252, 0.6) 100%)',
            border: '1px solid #E5E7EB',
            borderRadius: '6px',
            padding: '8px 12px',
            fontSize: '12px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            zIndex: 1000,
            transform: 'translateX(-50%)',
            whiteSpace: 'nowrap'
          }"
          class="hover-tooltip"
          @mouseenter="isHoveringTooltip = true"
          @mouseleave="isHoveringTooltip = false"
        >
          <div style="margin-bottom: 4px; color: #666;">{{ hoverTooltip.time }}</div>
          <div style="display: flex; align-items: center; gap: 4px;">
            <span>降雨量</span>
            <a-input-number
              v-model="hoverTooltip.inputValue"
              size="small"
              :min="0"
              :step="0.1"
              :precision="1"
              style="width: 60px;"
              @change="handleTooltipInputChange"
              @pressEnter="handleTooltipInputChange"
            />
            <span>mm</span>
          </div>
          <div style="margin-top: 4px; font-size: 10px; color: #999;">
            点击柱子Y轴位置可快速设值
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧：降雨过程 -->
    <div class="right-panel">
      <div class="panel-header">
        <h3>降雨过程</h3>
        <a-button type="primary" @click="handleBatchImport" class="batch-import-btn">
          批量导入
        </a-button>
      </div>
      
      <div class="table-section">
        <VxeTable
          v-if="columns.length > 0"
          ref="vxeTableRef"
          :isShowTableHeader="false"
          :isShowSetBtn="false"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :tablePage="false"
          :showFooter="true"
          :footerData="footerData"
          :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold', fontSize: '14px' }"
        ></VxeTable>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getInWaterRange, getRainfallList } from '../../../services'
  import VxeTable from '@/components/VxeTable/index.vue'
  import * as echarts from 'echarts'

  export default {
    name: 'RainfallList',
    props: ['baseInfo'],
    components: { VxeTable },
    data() {
      return {
        loading: false,
        list: [],
        columns: [],
        scaleValue: 1.0,
        chart: null,
        siteNames: [], // 存储站点名称
        footerData: [], // 表格底部汇总数据
        hoverTooltip: {
          show: false,
          x: 0,
          y: 0,
          time: '',
          inputValue: 0,
          barIndex: -1
        },
        isHoveringTooltip: false,
        hoveredRowIndex: -1, // 当前悬浮的表格行索引
        crosshairGraphics: [], // 十字线图形元素
        currentMousePos: { x: 0, y: 0 }, // 当前鼠标位置
      }
    },
    computed: {
      // 计算各时间段的总降雨量
      timeRainfallData() {
        return this.list.map(item => {
          return {
            time: item.tm,
            rainfall: +(item.totalRainfall * this.scaleValue).toFixed(1)
          }
        })
      }
    },
    watch: {
      list: {
        handler() {
          // 更新图表数据
          this.updateChart()
          // 更新表格底部汇总
          this.updateFooterData()
        },
        deep: true,
      },
      scaleValue() {
        this.updateChart()
      }
    },
    mounted() {
      this.initChart()
      this.loadData()
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
      }
    },
    methods: {
      // 初始化图表
      initChart() {
        this.chart = echarts.init(this.$refs.chartContainer)
        
        // 图表鼠标移动事件
        this.chart.getZr().on('mousemove', (e) => {
          const pointInPixel = [e.offsetX, e.offsetY]
          const pointInGrid = this.chart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)
          
          // 更新鼠标位置
          this.currentMousePos = { x: e.offsetX, y: e.offsetY }
          
          if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < this.timeRainfallData.length) {
            const barIndex = Math.round(pointInGrid[0])
            
            // 计算柱子的x坐标
            const barX = this.chart.convertToPixel({ seriesIndex: 0 }, [barIndex, 0])[0]
            
            // 显示悬浮框
            this.hoverTooltip = {
              show: true,
              x: barX,
              y: 20, // 固定在柱子上方
              time: this.timeRainfallData[barIndex].time,
              inputValue: this.timeRainfallData[barIndex].rainfall,
              barIndex: barIndex
            }
            
            // 更新十字线
            this.updateCrosshair(pointInPixel, pointInGrid, barIndex)
            
            this.chart.getZr().dom.style.cursor = 'pointer'
          } else {
            this.hoverTooltip.show = false
            this.hideCrosshair()
            this.chart.getZr().dom.style.cursor = 'default'
          }
        })

        // 图表鼠标离开事件
        this.chart.getZr().on('globalout', () => {
          // 延迟隐藏，给用户时间操作输入框
          setTimeout(() => {
            if (!this.isHoveringTooltip) {
              this.hoverTooltip.show = false
              this.hideCrosshair()
            }
          }, 100)
        })

        // 图表点击事件 - 支持Y轴方向调整
        this.chart.getZr().on('click', (e) => {
          const pointInPixel = [e.offsetX, e.offsetY]
          const pointInGrid = this.chart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)
          
          if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < this.timeRainfallData.length) {
            const barIndex = Math.round(pointInGrid[0])
            const clickedValue = pointInGrid[1] // Y轴位置对应的数值
            
            // 确保点击的Y值为正数
            if (clickedValue >= 0) {
              this.handleBarYAxisClick(barIndex, Math.round(clickedValue * 10) / 10)
            }
          }
        })
      },

      // 更新图表
      updateChart() {
        if (!this.chart || this.timeRainfallData.length === 0) return
        
        const option = {
          title: {
            left: 'center',
            textStyle: {
              fontSize: 14
            }
          },
          tooltip: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: this.timeRainfallData.map(item => item.time),
            axisLabel: {
              rotate: 45,
              fontSize: 10,
              interval: 'auto',
              margin: 15
            },
            axisTick: {
              alignWithLabel: true
            },
            axisPointer: {
              type: 'shadow'
            }
          },
          yAxis: {
            type: 'value',
            name: '降雨量(mm)',
            nameTextStyle: {
              fontSize: 12
            }
          },
          series: [{
            data: this.timeRainfallData.map(item => item.rainfall),
            type: 'bar',
            itemStyle: {
              color: '#165DFF'
            },
            barMaxWidth: 30, // 使用更小的固定宽度（像素）
            barGap: '60%', // 柱子之间的间距设为柱子宽度的80%
          }],
          grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            containLabel: true
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ]
        }

        // 如果有十字线，添加到配置中
        if (this.crosshairGraphics && this.crosshairGraphics.length > 0) {
          option.graphic = this.crosshairGraphics
        }

        this.chart.setOption(option, true)
        
        // 重新调整图表大小以适应容器
        this.chart.resize()
      },

      // 更新指定时间的降雨量
      updateTimeRainfall(timeIndex, newValue) {
        // 直接更新总降雨量
        const actualValue = newValue / this.scaleValue
        this.list[timeIndex].totalRainfall = +actualValue.toFixed(1)
        
        // 按比例更新站点降雨量
        const originalTotal = Object.values(this.list[timeIndex].sitesObj).reduce((sum, val) => {
          return sum + (typeof val === 'number' ? val : 0)
        }, 0)
        
        if (originalTotal > 0) {
          const ratio = actualValue / originalTotal
          Object.keys(this.list[timeIndex].sitesObj).forEach(siteId => {
            if (typeof this.list[timeIndex].sitesObj[siteId] === 'number') {
              this.list[timeIndex].sitesObj[siteId] = +(this.list[timeIndex].sitesObj[siteId] * ratio).toFixed(1)
            }
          })
        }
      },

      // 处理缩放系数变化
      handleScaleChange() {
        this.updateChart()
      },

      // 处理表格降雨量变化
      handleTableRainfallChange(rowIndex, value) {
        this.list[rowIndex].totalRainfall = value || 0
        
        // 按比例更新站点降雨量
        const originalTotal = Object.values(this.list[rowIndex].sitesObj).reduce((sum, val) => {
          return sum + (typeof val === 'number' ? val : 0)
        }, 0)
        
        if (originalTotal > 0 && value > 0) {
          const ratio = value / originalTotal
          Object.keys(this.list[rowIndex].sitesObj).forEach(siteId => {
            if (typeof this.list[rowIndex].sitesObj[siteId] === 'number') {
              this.list[rowIndex].sitesObj[siteId] = +(this.list[rowIndex].sitesObj[siteId] * ratio).toFixed(1)
            }
          })
        }
        
        // 触发图表更新
        this.updateChart()
      },

      // 处理柱状图Y轴点击
      handleBarYAxisClick(barIndex, clickedValue) {
        // 根据点击的Y轴位置更新降雨量
        this.updateTimeRainfall(barIndex, clickedValue)
        
        // 同时更新悬浮框的值
        if (this.hoverTooltip.show && this.hoverTooltip.barIndex === barIndex) {
          this.hoverTooltip.inputValue = clickedValue
        }
      },

      // 处理悬浮框输入变化
      handleTooltipInputChange() {
        if (this.hoverTooltip.barIndex >= 0 && this.hoverTooltip.inputValue >= 0) {
          this.updateTimeRainfall(this.hoverTooltip.barIndex, this.hoverTooltip.inputValue)
        }
      },

      // 处理表格向下填充
      handleFillDown(fromIndex) {
        const fillValue = this.list[fromIndex].totalRainfall
        
        // 向下填充到所有后续行
        for (let i = fromIndex + 1; i < this.list.length; i++) {
          this.list[i].totalRainfall = fillValue
          
          // 同时更新站点数据
          const originalTotal = Object.values(this.list[i].sitesObj).reduce((sum, val) => {
            return sum + (typeof val === 'number' ? val : 0)
          }, 0)
          
          if (originalTotal > 0 && fillValue > 0) {
            const ratio = fillValue / originalTotal
            Object.keys(this.list[i].sitesObj).forEach(siteId => {
              if (typeof this.list[i].sitesObj[siteId] === 'number') {
                this.list[i].sitesObj[siteId] = +(this.list[i].sitesObj[siteId] * ratio).toFixed(1)
              }
            })
          }
        }
        
        // 更新图表
        this.updateChart()
        
        // 隐藏悬浮行
        this.hoveredRowIndex = -1
      },

      // 更新表格底部汇总数据
      updateFooterData() {
        if (this.list.length === 0) {
          this.footerData = []
          return
        }
        
        const totalRainfall = this.list.reduce((sum, item) => {
          return sum + (item.totalRainfall || 0)
        }, 0)
        
        this.footerData = [{
          totalRainfall: +totalRainfall.toFixed(1)
        }]
      },

      // 更新十字线
      updateCrosshair(pointInPixel, pointInGrid, barIndex) {
        if (!this.chart) return
        const grid = this.chart.getModel().getComponent('grid', 0)
        const gridRect = grid.coordinateSystem.getArea()
        // 计算柱子中心的X坐标
        const barCenterX = this.chart.convertToPixel({ seriesIndex: 0 }, [barIndex, 0])[0]
        // 获取当前柱子的降雨量（优先用hoverTooltip.inputValue，否则用数据）
        let rainfallValue = this.hoverTooltip && this.hoverTooltip.barIndex === barIndex && typeof this.hoverTooltip.inputValue === 'number'
          ? this.hoverTooltip.inputValue
          : (this.timeRainfallData[barIndex] ? this.timeRainfallData[barIndex].rainfall : 0)
        // 用降雨量值转y像素
        const barY = this.chart.convertToPixel({ seriesIndex: 0 }, [barIndex, rainfallValue])[1]
        
        // 清除旧的十字线
        this.chart.getZr().remove(this.crosshairGraphics);
        this.crosshairGraphics = [];
        
        // 创建新的十字线图形元素
        const verticalLine = {
          type: 'line',
          shape: {
            x1: barCenterX,
            y1: gridRect.y,
            x2: barCenterX,
            y2: gridRect.y + gridRect.height
          },
          style: {
            stroke: '#FF6B6B',
            lineWidth: 1,
            lineDash: [4, 4],
            opacity: 0.8
          },
          silent: true,
          z: 100
        };
        
        const horizontalLine = {
          type: 'line',
          shape: {
            x1: gridRect.x,
            y1: barY,
            x2: gridRect.x + gridRect.width,
            y2: barY
          },
          style: {
            stroke: '#FF6B6B',
            lineWidth: 1,
            lineDash: [4, 4],
            opacity: 0.8
          },
          silent: true,
          z: 100
        };
        
        const yAxisLabel = {
          type: 'text',
          position: [gridRect.x - 5, barY],
          style: {
            text: rainfallValue.toFixed(1),
            fill: '#FF6B6B',
            fontSize: 12,
            fontWeight: 'bold',
            textAlign: 'right',
            textVerticalAlign: 'middle',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#FF6B6B',
            borderWidth: 1,
            borderRadius: 3,
            padding: [2, 4]
          },
          silent: true,
          z: 100
        };
        
        this.crosshairGraphics = [verticalLine, horizontalLine, yAxisLabel];
        
        // 直接将图形元素添加到zrender层
        const zr = this.chart.getZr();
        zr.add(verticalLine);
        zr.add(horizontalLine);
        zr.add(yAxisLabel);
      },

      // 隐藏十字线
      hideCrosshair() {
        if (this.chart && this.crosshairGraphics.length > 0) {
          const zr = this.chart.getZr();
          this.crosshairGraphics.forEach(graphic => {
            zr.remove(graphic);
          });
          this.crosshairGraphics = [];
        }
      },

      // 批量导入
      handleBatchImport() {
        this.$message.info('批量导入功能待开发')
      },

      // 加载数据
      loadData() {
        // 注释掉真实接口请求，使用测试数据
        /*
        getInWaterRange({ fcstRange: this.baseInfo.fcstRange }).then(res => {
          // 原接口逻辑
        })
        */

        // 生成测试数据
        this.generateTestData()
      },

      // 生成测试数据
      generateTestData() {
        this.loading = true
        
        // 模拟延迟
        setTimeout(() => {
          // 模拟站点数据
          const mockSites = [
            { siteId: 'site001', siteName: '测站1' },
            { siteId: 'site002', siteName: '测站2' },
            { siteId: 'site003', siteName: '测站3' },
            { siteId: 'site004', siteName: '测站4' }
          ]

          // 生成列配置
          this.columns = [
            { 
              title: '序号', 
              field: 'index', 
              width: 60, 
              slots: { 
                default: ({ rowIndex }) => rowIndex + 1,
                footer: () => '总计'
              } 
            },
            { 
              title: '时间', 
              field: 'tm', 
              minWidth: 150,
              slots: {
                footer: () => '--'
              }
            },
            { 
              title: '时段降水量(mm)', 
              field: 'totalRainfall', 
              minWidth: 200,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div 
                      class='rainfall-cell'
                      onMouseenter={() => this.hoveredRowIndex = rowIndex}
                      onMouseleave={() => this.hoveredRowIndex = -1}
                      style="display: flex; align-items: center; gap: 8px; width: 100%;"
                    >
                      <a-input-number 
                        size='small' 
                        step={0.1} 
                        min={0} 
                        precision={1}
                        v-model={this.list[rowIndex].totalRainfall}
                        onChange={(value) => this.handleTableRainfallChange(rowIndex, value)}
                        style="width: 120px;"
                      />
                      <a
                        style={{
                          color: '#165DFF', 
                          textDecoration: 'none', 
                          fontSize: '12px', 
                          whiteSpace: 'nowrap',
                          opacity: this.hoveredRowIndex === rowIndex ? 1 : 0,
                          transition: 'opacity 0.2s ease',
                          cursor: 'pointer'
                        }}
                        onClick={() => this.handleFillDown(rowIndex)}
                      >
                        向下填充
                      </a>
                    </div>
                  )
                },
                footer: ({ row }) => {
                  return this.footerData.length > 0 ? this.footerData[0].totalRainfall + ' mm' : '0 mm'
                }
              }
            }
          ]

          // 生成时间序列数据（24小时）
          const mockTimeData = []
          const baseTime = new Date()
          for (let i = 0; i < 24; i++) {
            const time = new Date(baseTime.getTime() + i * 60 * 60 * 1000)
            const timeStr = time.toISOString().slice(0, 16).replace('T', ' ')
            
            const sitesObj = {}
            let totalRainfall = 0
            mockSites.forEach(site => {
              // 生成随机降雨量数据
              const rainfall = +(Math.random() * 20).toFixed(1)
              sitesObj[site.siteId] = rainfall
              totalRainfall += rainfall
            })
            
            mockTimeData.push({
              tm: timeStr,
              sitesObj,
              totalRainfall: +totalRainfall.toFixed(1)
            })
          }

          this.list = mockTimeData
          this.siteNames = mockSites.map(site => site.siteName)
          this.loading = false
          
          // 初始化图表和底部汇总
          this.$nextTick(() => {
            this.updateChart()
            this.updateFooterData()
          })
        }, 1000)
      },

      save() {
        this.$emit(
          'saveData',
          this.list.map(el => {
            const arr = []
            Object.keys(el.sitesObj).forEach(key => {
              arr.push({ siteId: key, rain: el.sitesObj[key] })
            })
            return { tm: el.tm, sites: arr }
          }),
        )
      },
    },
  }
</script>

<style lang="less" scoped>
  .rainfall-container {
    display: flex;
    height: 100%;
    gap: 20px;
  }

  .left-panel {
    flex: 1.86;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    padding: 16px;
    overflow: hidden; // 防止内容溢出
  }

  .right-panel {
    flex: 1.4;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    padding: 16px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .batch-import-btn {
    border-color: #165DFF;
    color: #165DFF !important;
    background-color: #fff !important;
    // &:hover {
    //   border-color: #3273FF;
    //   color: #3273FF;
    // }
  }

  .scale-section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    label {
      white-space: nowrap;
      margin-right: 8px;
      font-weight: 500;
    }
  }

  .chart-section {
    flex: 1;
    min-height: 500px;
    position: relative;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background-color: #F7F8FA;
    overflow: hidden;
  }

  .hover-tooltip {
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateX(-50%) scale(1.02);
    }
  }

  .rainfall-cell {
    transition: all 0.2s ease;
    
    &:hover {
      background-color: rgba(22, 93, 255, 0.05);
      border-radius: 4px;
      padding: 2px 4px;
      margin: -2px -4px;
    }
    
    a {
      transition: all 0.2s ease;
      
      &:hover {
        color: #3273FF !important;
        text-decoration: underline !important;
      }
    }
  }

  .table-section {
    flex: 1;
    overflow: auto;
  }

  .cell-box {
    a {
      display: none;
      color: #165DFF;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    &:hover {
      a {
        display: inline;
      }
    }
  }
</style>
