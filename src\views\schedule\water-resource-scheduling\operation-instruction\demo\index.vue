<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="操作票编号">
        <a-input v-model="queryParam.operateCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="操作任务">
        <a-input v-model="queryParam.operateTask" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="操作日期">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.optTime"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <a-form-item label="工作票编号">
        <a-input v-model="queryParam.cmdCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleSync()">
              <a-icon type="plus" />
              同步
            </a-button>
          </div>
        </VxeTable>
        <OptCmdDetail v-if="showForm" ref="formRef" @close="showForm = false" />
        <RunCmdDetail v-if="runCmdshowForm" ref="runCmdFormRef" @close="runCmdshowForm = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOperateCmdPage, syncOperateCmd } from './services'
  import OptCmdDetail from './modules/OptCmdDetail.vue'
  import RunCmdDetail from '../../dispatch-instruction/demo/modules/RunCmdDetail.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'OperateCmd',
    components: { VxeTable, VxeTableForm, OptCmdDetail, RunCmdDetail },
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        isChecked: false,
        showForm: false,
        runCmdshowForm: false,
        list: [],
        tableTitle: '操作指令',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          operateTask: '',
          operateCode: '',
          cmdCode: '',
          startDate: null,
          endDate: null,
          optTime: [],
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '操作票编号',
            field: 'operateCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                if (row.runCmdId) {
                  return <span>{row.operateCode}</span>
                } else {
                  return <span style='color:red'>{row.operateCode}</span>
                }
              },
            },
          },
          { title: '工程名称', field: 'cmdName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作内容', field: 'operateTask', minWidth: 140, showOverflow: 'tooltip' },
          {
            title: '关联的工程票',
            field: 'cmdCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleRunCmd(row)} disabled={!row.runCmdId}>
                      {row.cmdCode}
                    </a>
                  </span>
                )
              },
            },
          },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          { 
            title: '操作时间', 
            field: 'operateTime', 
            minWidth: 180, 
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                const startTime = row.startDate || '';
                const endTime = row.endDate || '';
                return <span>{startTime} - {endTime}</span>
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleOptCmd(row)}>查看</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      openAttach(row) {
        window.open(row.attachUrl)
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },

      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },

      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.yearShowOne = false
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.runCmdshowForm = false
        this.loading = true
        this.selectChange({ records: [] })
        this.queryParam.sort = [{ direction: 'desc', property: 'startDate' }]
        getOperateCmdPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.dispatchId)
        this.names = valObj.records.map(item => item.dispatchCode)
        this.isChecked = !!valObj.records.length
      },
      handleSync() {
        this.loading = true
        syncOperateCmd().then(response => {
          this.loading = false
          let size = response?.data
          this.$message.success(`本次同步${size}条记录`, 3)
          if (size > 0) {
            this.resetQuery()
          }
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          operateTask: '',
          operateCode: '',
          cmdCode: '',
          startDate: null,
          endDate: null,
          optTime: [],
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      handleRunCmd(record) {
        this.runCmdshowForm = true
        this.$nextTick(() => this.$refs.runCmdFormRef.handle(record))
      },

      handleOptCmd(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
    },
  }
</script>
<style lang="less" scoped></style>
