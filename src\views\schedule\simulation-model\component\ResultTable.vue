<template>
  <div style="height: 100%; display: flex; flex-direction: column">
    <a-tabs v-model="active" size="small" :animated="false">
      <a-tab-pane v-for="(el, idx) in allData" :key="el.projectId" :tab="el.projectName"></a-tab-pane>
    </a-tabs>

    <template v-for="(el, idx) in allData">
      <div style="flex: 1" v-if="active === el.projectId">
        <VxeTable
          :key="el.projectId"
          ref="vxeTableRef"
          size="small"
          :isShowTableHeader="false"
          :isDrop="false"
          :columns="el.columns"
          :tableData="el.resVOS"
          :loading="loading"
          :tablePage="false"
          :scrollY="{ enabled: true, gt: 0 }"
          :scrollX="{ enabled: true, gt: 0 }"
        ></VxeTable>
      </div>
    </template>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'ResultTable',
    props: ['dataSource'],
    components: { VxeTable },
    data() {
      return {
        active: undefined,
        loading: false,
        allData: [],
      }
    },
    computed: {},
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.active = newVal?.[0]?.projectId
          this.allData = newVal.map((el, index) => {
            const resArr = el.resVOS.map(ele => {
              let obj = {}
              ele.records.forEach(ele => (obj[ele.projectId] = ele))
              return { ...ele, recordsObj: obj }
            })
            return {
              ...el,
              resVOS: resArr,
              columns: [
                { title: '调度时间', field: 'tm', fixed: 'left', minWidth: 140 },
                ...el.projects.map(ele => ({
                  title: ele.projectName,
                  children: [
                    {
                      field: `recordsObj.${ele.projectId}.upWlv`,
                      title: '上游水位(m)',
                      minWidth: 100,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          const obj = row.recordsObj[ele.projectId]
                          return obj.upWlv
                        },
                      },
                    },
                    {
                      field: `recordsObj.${ele.projectId}.downWlv`,
                      title: '下游水位(m)',
                      minWidth: 100,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          const obj = row.recordsObj[ele.projectId]
                          return obj.downWlv
                        },
                      },
                    },
                    {
                      title: '闸泵工况',
                      minWidth: 170,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          const obj = row.recordsObj[ele.projectId]
                          return obj.flow
                          
                        },
                      },
                    },
                  ],
                })),
              ],
            }
          })
        },
      },
    },
    created() {
      this.getData()
    },
    methods: {
      getData() {
        this.active = this.dataSource?.[0]?.projectId
        this.allData = this.dataSource.map((el, index) => {
          const resArr = el.resVOS.map(ele => {
            let obj = {}
            ele.records.forEach(ele => (obj[ele.projectId] = ele))
            return { ...ele, recordsObj: obj }
          })
          return {
            ...el,
            resVOS: resArr,
            columns: [
              { title: '调度时间', field: 'tm', fixed: 'left', minWidth: 140 },
              ...el.projects.map(ele => ({
                title: ele.projectName,
                children: [
                  {
                    field: `recordsObj.${ele.projectId}.upWlv`,
                    title: '上游水位(m)',
                    minWidth: 100,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        const obj = row.recordsObj[ele.projectId]
                        if (!obj) return ''
                        return obj.type === 3 ? '-' : obj.upWlv
                      },
                    },
                  },
                  {
                    field: `recordsObj.${ele.projectId}.downWlv`,
                    title: '下游水位(m)',
                    minWidth: 100,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        const obj = row.recordsObj[ele.projectId]
                        if (!obj) return ''
                        return obj.type === 3 ? '-' : obj.downWlv
                      },
                    },
                  },
                  {
                    title: '闸泵工况',
                    minWidth: 170,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        const obj = row.recordsObj[ele.projectId]
                        if (!obj) return ''

                        if (obj.type === 3) return '-'

                        if (obj.type === 0 || obj.type === 2) {
                          return (
                            <div class='cell-box'>
                              <span>闸流量:{obj.outFlow}</span>&nbsp;&nbsp;
                              <span>开度:{obj.open}</span>
                            </div>
                          )
                        }

                        if (obj.type === 1 || obj.type === 4) {
                          return <span>泵流量:{obj.inFlow}</span>
                        }
                      },
                    },
                  },
                ],
              })),
            ],
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  // ::v-deep .ant-tabs {
  //   height: 100%;
  //   .ant-tabs-content {
  //     height: calc(100% - 52px);
  //     .ant-tabs-tabpane {
  //       display: flex;
  //       flex-direction: column;
  //     }
  //   }
  // }
</style>
