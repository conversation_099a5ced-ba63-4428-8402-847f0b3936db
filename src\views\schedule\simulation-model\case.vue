<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 第一大区块 -->
    <div
      style="display: flex;justify-content: space-between;align-items: center;padding-bottom: 16px;border-bottom: 1px solid #f2f3f5;">
      <div>
        <a-select v-model="selectedForecast" style="width: 350px" placeholder="请选择预报数据">
          <a-select-option v-for="item in forecastOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
      <div>
        <a-button type="primary" style="color: #fff; background: #165dff; font-size: 14px; font-weight: 400"
          @click="goToComingWaterForecast">
          方案管理
        </a-button>
      </div>
    </div>

    <!-- 内容 -->
    <div style="position: relative; display: flex; flex-direction: row; height: 660px;width: 100%;">
      <ShowModelComponent :chSimId="dataSource.chSimId" :modelId="dataSource.modelId"/>
    </div>
  </div>
</template>

<script>
import { getChSimPage } from './services.js'
import ShowModelComponent from './ShowModelComponent.vue'
export default {
  name: 'ManualForecast',
  data() {
    return {
      selectedForecast: undefined,
      forecastOptions: [],
      forecastList: [],
      dataSource:{}
    }
  },
  watch: {
    selectedForecast(newValue, oldValue) {
      this.dataSource = this.forecastList.find(el => el.chSimId == newValue)
    }
  },
  components: { ShowModelComponent },
  async created() {
    // 初始化模型下拉列表选项默认显示时间上最新的前五项
    await getChSimPage({
      "pageNum": 1,
      "pageSize": 5,
      "sort": [],
      "userId": null
    }).then(res => {
      if (res.data.data.length > 0) {
        this.forecastList = res.data.data
        this.forecastOptions = res.data.data.map(el => ({ label: el.caseName, value: el.chSimId, modelId: el.modelId }))
        this.selectedForecast = this.forecastOptions[0].value
        this.dataSource = this.forecastList.find(el => el.chSimId == this.selectedForecast)
      } else {
        this.forecastOptions = []
        this.selectedForecast = ""
      }
    }).catch(err => {
      console.log(err)
    })
  },
  methods: {
    goToComingWaterForecast() {
      this.$router.push('/schedule/simulation-model-case')
    },
  }
}
</script>

<style lang="less" scoped>
.summary {
  display: flex;
  justify-content: space-between;

  .hgroup {
    width: 19%;
    padding: 24px 26px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;

    .content {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .num {
      font-weight: 700;
      font-size: 24px;
      color: #1d2129;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 0;
    }

    .text {
      font-size: 14px;
      color: #4e5969;
      font-weight: 400;
      margin: 0;
    }

    .unit {
      margin-left: -2px;
      font-size: 14px;
    }

    .icon {
      width: 50px;
      height: 50px;
      display: inline-block;
      flex-shrink: 0;
    }

    &:nth-child(1) {
      .icon {
        background: url('@/assets/images/three-days-rain.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(2) {
      .icon {
        background: url('@/assets/images/all-rain.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(3) {
      .icon {
        background: url('@/assets/images/coming-water.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(4) {
      .icon {
        background: url('@/assets/images/flood-peak.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(5) {
      .icon {
        background: url('@/assets/images/flood-peak-time.png') 0 0 no-repeat;
        background-size: 100%;
      }

      .num {
        font-size: 15px;
      }
    }
  }
}

.flood-box {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;

  .flood-tabs {
    margin: 0;

    .name {
      font-size: 20px;
      color: #1d2129;
      font-weight: 600;
    }
  }

  .flood-content {
    flex: 1;
  }
}

@font-face {
  font-family: 'AlimamaDaoLiTi';
  src: url('@/assets/font/AlimamaDaoLiTi.ttf');
}

.curve-panel {
  position: absolute;
  z-index: 1000;
  bottom: 4px;
  right: 4px;
  width: 700px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;

  .left {
    border-right: 1px solid #e5e6eb;
    width: 150px;
    position: relative;
    display: flex;
    flex-direction: column;

    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }

      .name {
        flex: 1;
        margin: 0 0 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .indicator {
      display: flex;
      padding: 6px 8px;
      justify-content: space-between;

      .label {
        color: '#4E5969';
      }

      .value {
        color: #1d2129;
      }
    }
  }

  .right {
    flex: 1;
    padding-top: 10px;
  }
}
</style>
